[2025-07-04 16:58:33] 测试日志内容 - 这应该被保留
[2025-07-04 16:58:33] 新追加的日志内容
[2025-07-04 16:58:40] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:58:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:14] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:14] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:14] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:59:32] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:00:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:00:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:00:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:00:32] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:00:32] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:00:32] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:00:38] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:00:38] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:21] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:21] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:21] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:35] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:35] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:35] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:41] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:41] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:03:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:04:28] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:04:28] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析完成 - 数据源:客户端AI分析, 摘要:- **总体情况**： - 测试总数703次，失败33次，失败率4.69%（行业一般标准应<3%），需重点关注。 - **Top 3失败项**占75.76%： - **section_4.7.2 an | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:35] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:43] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:43] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:43] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:47] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:07:47] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
37.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:33] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:13:58] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:53] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:20:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:25:53] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:25:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:27:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:27:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:27:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:27:47] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:27:47] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:27:47] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:28:40] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:28:40] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:30:53] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:30:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
37.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:33:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:48] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:35:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:36:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:36:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:36:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:36:04] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:36:04] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:36:04] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:36:06] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:36:06] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:42:59] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:43:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:43:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:43:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:43:11] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:43:11] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:43:11] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:43:15] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 17:43:15] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:03] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:03] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:38:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:39:47] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:39:47] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:39:47] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:39:50] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:39:50] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:39:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:40:44] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:40:44] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析完成 - 数据源:客户端AI分析, 摘要:- **测试总量**：944台次 - **失败数量**：66台次 - **整体失败率**：6.99%（行业基准通常<5%，需引起重视） 1. **产品信息读取(section_2.5)**：23次(3 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:34] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:34] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:34] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:38] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:43:38] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:44:50] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:44:50] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:44:50] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:44:59] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:44:59] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:45:13] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:45:13] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:45:13] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:45:16] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:45:16] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:48:27] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:48:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:49:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:53:27] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:53:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:44] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:44] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:44] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:48] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:48] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:54:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/access_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:55:38] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:55:38] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析完成 - 数据源:客户端AI分析, 摘要:- **测试总量**：954台次 - **失败数量**：66台次 - **总体失败率**：6.92%（行业基准通常在3-5%之间，偏高） 1. **产品信息读取(section_2.5)** - 23 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:57:20] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:57:20] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:57:20] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:57:24] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:57:24] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:58:27] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:58:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:59:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 22:59:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:27] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:46] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:46] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:46] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:46] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:54] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:54] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:03:54] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:00] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:00] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:15] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
37.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:20] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 23:04:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:43] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:56] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:52:56] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:53:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:53:10] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:53:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:53:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:53:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:53:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:53:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:53:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:53:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:53:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:57:00] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 11:57:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:02:01] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:02:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:12:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:02] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:03] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:03] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:22] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:22] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:22] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:23] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:24] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:18:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:21:03] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:21:03] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析完成 - 数据源:客户端AI分析, 摘要:--- - 总测试数：404 - 失败测试数：18 - 失败率：4.46%（该值略高于汽车行业EOL测试的平均失败率（通常<3%），表明生产线存在潜在问题，需重点关注）。 - 主要失败项集中在前三个s | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:23:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:23:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:25:41] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:25:41] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:25:41] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:25:50] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:25:50] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:26:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:28:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:28:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:33:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:33:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:33] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:33] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:33] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:33] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:33] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:38:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:40:02] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:40:02] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:40:02] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:40:05] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:40:05] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:40:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:41:23] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:41:23] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析完成 - 数据源:客户端AI分析, 摘要:- **核心问题**：多媒体系统（音频+摄像头）失效占比高达**79.17%**（19/24次），是主要瓶颈 - **异常点**：`section_0.0.0`（初始化失败）暗示测试环境或设备问题 - | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:42:47] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:42:47] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:42:47] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:42:50] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:42:50] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:43:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:43:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:43:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:43:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:48:28] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:48:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:48:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:48:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:53:28] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:53:29] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:53:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 12:53:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:03] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:03] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:10:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:15:05] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:15:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:20:05] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:20:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:49:19] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:50:05] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 13:55:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 14:00:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 14:08:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-05 15:00:43] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:21] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:23] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:23] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:30] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:30] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:30] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:33] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:25:33] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析取消 - 用户取消了收费确认 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:27:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:28:10] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:28:10] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:28:10] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:28:12] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:28:12] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:28:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:30:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:30:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:31:17] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:31:17] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析完成 - 数据源:客户端AI分析, 摘要:### 汽车EOL测试数据分析报告

作为专业的汽车测试数据分析专家，我已对提供的EOL测试数据进行了深入分析。数据来自EOL2站位今天的测试（总测试数：531，失败测试数：32，失败率：6.03%） | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:31:39] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:31:39] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:31:39] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:31:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:31:39] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:31:39] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:31:39] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:32:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:32:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:34:59] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:35:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:35:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:36:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:37:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:37:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:40:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:40:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:42:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:42:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:45:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:45:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:37] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:37] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:37] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:37] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:37] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:37] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:37] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:37] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:37] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:38] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:38] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:51:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:56:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 15:56:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:01:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:01:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:06:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:06:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:11:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:11:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:16:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:16:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:22:25] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:22:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:26:55] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:26:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:26:59] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:26:59] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:26:59] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:27:01] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:27:01] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析开始 - 时间范围:0, 站位:SOC, 限制:10 | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:27:01] IP: 127.0.0.1 | Method: GET | Path: /api/deepseek/balance | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:27:08] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:27:08] IP: 127.0.0.1 | Method: GET | Path: /api/ai_config | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:27:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/ai_analysis_data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:31:35] IP: 127.0.0.1 | Method: POST | Path: /api/log_ai_analysis | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:31:35] IP: 127.0.0.1 | Method: AI_ANALYSIS | Path: AI分析完成 - 数据源:客户端AI分析, 摘要:- **时间与站位：** 今天（EOL2站位），表明数据来自当前生产线的末端测试点，可能涉及整车电子系统集成测试。 - **整体性能：** 总测试567次，失败34次，失败率6.00%。高于行业基准（ | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:31:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:31:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:32:27] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:32:27] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:32:27] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:32:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:32:27] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:32:27] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-10 16:32:27] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:28] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:28] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:28] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:28] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:28] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:28] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:28] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:29] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:29] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:29] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:29] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:33] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:33] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 10:58:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:03:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:03:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:07:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:07:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:07:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:07:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/failure_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:08:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:08:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:13:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:13:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:18:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:18:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:23:29] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-15 11:23:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:47] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:48] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:48] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:48] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:48] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:49] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:49] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:49] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:50] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:52] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:43:53] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:47:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:48:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:48:49] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:48:51] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:57:40] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:57:40] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:57:40] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:57:40] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:57:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:57:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:57:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:57:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:04] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:04] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 09:58:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:00:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:00:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:00:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:00:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:00:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:00:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:00:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:03:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:03:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:04:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:04:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:04:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:04:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:04:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:04:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:08:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:08:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:13:04] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:13:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:33] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:33] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:33] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:34] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:34] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:34] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:46] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:53] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:54] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:54] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:55] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:14:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:15:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:15:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:15:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:15:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:15:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:15:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:15:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:15:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:18:05] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:18:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:05] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:05] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-29 10:19:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:05] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:05] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:06] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:25] IP: 127.0.0.1 | Method: GET | Path: /api/read_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:26] IP: 127.0.0.1 | Method: GET | Path: /api/read_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:19:38] IP: 127.0.0.1 | Method: GET | Path: /api/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:20:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:20:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:20:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:20:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:20:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:20:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:20:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:20:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:20:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:20:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:21:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:23:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:23:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:23:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:23:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:23:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:23:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:23:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:24:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:24:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:24:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:29:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:29:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:34:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:34:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:39:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:39:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:43:52] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:43:52] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:43:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:44:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:44:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:49:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:49:06] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:31] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:31] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:31] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:31] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:31] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:31] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:52:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:32] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:57:59] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:58:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:58:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 10:59:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:02:32] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:02:33] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:07:32] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:07:33] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:12:32] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:12:33] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:17:32] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:17:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:22:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:22:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:27:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:27:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:32:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:32:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:37:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:37:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:42:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:42:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:47:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:47:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:52:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:52:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:57:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 11:57:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:02:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:02:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:07:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:07:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:12:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:12:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:17:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:17:51] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:22:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:22:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:27:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:27:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:32:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:32:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:37:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:37:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:42:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:42:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:47:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:47:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:52:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:52:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:57:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 12:57:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:02:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:02:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:20] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:20] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:20] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:20] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:20] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:20] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:21] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
/537.36
[2025-07-29 13:06:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:30] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:33] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:06:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:08:22] IP: 127.0.0.1 | Method: GET | Path: /api/read_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:08:24] IP: 127.0.0.1 | Method: GET | Path: /api/read_log | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:08:26] IP: 127.0.0.1 | Method: GET | Path: /api/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:20] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:20] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:20] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:20] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:20] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:21] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:21] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:21] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:21] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:32] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:32] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:32] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:32] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:32] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:33] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:33] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-29 13:12:33] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:33] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:33] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:33] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:49] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:12:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:50] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:50] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:50] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:51] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:52] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:52] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:53] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:14:53] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:21] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:21] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:21] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:21] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:21] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:22] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:22] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:22] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:16:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:17:33] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:17:34] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:17:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:17:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:17:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:17:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:17:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:17:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:17:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:17:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:19:51] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:19:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:13] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:57:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:58:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:58:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:58:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:58:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:58:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 13:59:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:02:14] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:02:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:15] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:57] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:59] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:07:59] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:02] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:08:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:33] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:33] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:13:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:01] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:03] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:03] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:04] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:11] IP: 127.0.0.1 | Method: GET | Path: /favicon.ico | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:15] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:15] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:15] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:15] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:19:15] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:23:34] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:23:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:57] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:57] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:57] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-07-29 14:25:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:58] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:58] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:58] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:58] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:25:58] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:02] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:03] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:05] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:09] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:26:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:30:58] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:30:59] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:02] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:04] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:11] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:11] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:11] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:11] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:15] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:16] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:44] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:44] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:44] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:45] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:45] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:44:45] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:45:04] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:45:04] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:45:04] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
i/537.36
[2025-07-29 14:45:04] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:45:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:13] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:13] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:13] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:13] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
6
[2025-07-29 14:48:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:13] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:14] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:14] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:14] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:14] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:14] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:19] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:21] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:48:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:49:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:49:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:52:46] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:52:46] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
.0 Safari/537.36
[2025-07-29 14:52:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:52:46] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:52:46] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:52:46] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:53:14] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:53:15] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:54:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:54:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:36] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:36] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:36] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:36] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:36] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:36] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:36] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:38] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:55:50] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:59:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 14:59:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:00:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:00:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:57] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:57] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:58] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:59] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:01:59] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:02:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:02:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:02:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:02:03] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:02:03] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:02:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:02:05] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:02:10] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:37] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:37] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:37] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:38] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:04:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:25] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:26] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:26] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:26] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:35] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:37] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:39] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:05:47] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:05] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:05] IP: 127.0.0.1 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:05] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:05] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:05] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:05] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:06] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:06] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:06] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:06] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:06] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:06] IP: 127.0.0.1 | Method: GET | Path: /sm/f07d8d7b2652873f485707eab4f3d300bf1f6f3b42912e189c8933b1b9b3dfde.map | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:07] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:08] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:08] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:09] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:20] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:08:55] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:09:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:09:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:09:12] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:09:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:09:16] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:09:17] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:09:17] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:09:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:09:25] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:11:53] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:11:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:12:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:12:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:12:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:12:28] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:12:28] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:12:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:12:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:12:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:13:06] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:13:07] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:34] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:34] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:36] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:37] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:38] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:38] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:39] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:40] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:48] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:49] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-29 15:14:52] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:39] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:40] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:41] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:41] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:42] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:48:46] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-31 11:49:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:07] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:08] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:10] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:10] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:10] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:10] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:11] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:11] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:11] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:12] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:12] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 17:58:13] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
6
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:43] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:44] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:01:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:02:59] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:03:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:03:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:03:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:03:00] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:03:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:03:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:03:00] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:22] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:22] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:22] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:22] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:22] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:22] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:22] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:22] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:22] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:23] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:23] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:23] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:23] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:23] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 18:54:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:23] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:23] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

[2025-08-01 20:44:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:23] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:24] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:24] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:24] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:24] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:24] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:25] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:25] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:25] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:25] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:26] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:26] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:27] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:27] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:29] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:30] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:31] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 20:44:36] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:31] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:34] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:35] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:42] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:43] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:44] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:45] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:45] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:48] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/log_detail | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:14:53] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/item_trend | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:19:31] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-01 21:19:32] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:23] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:23] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:23] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:23] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:23] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:05:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:07:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:07:01] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:07:01] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:09:21] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:09:22] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:09:22] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:10:24] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:10:24] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:11:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:11:11] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 18:11:11] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
