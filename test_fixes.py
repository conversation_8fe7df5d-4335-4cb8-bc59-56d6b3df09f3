#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import get_display_device_name, get_original_device_names, get_station_condition_sql

def test_device_mapping_fixes():
    """测试设备映射修复"""
    print("🔧 测试设备映射修复...")
    
    # 测试新VIU设备映射
    print("\n1. 测试新VIU设备映射:")
    viu_devices = ['VIU', 'VIU_1', 'VIU_2', 'VIU_3']
    for device in viu_devices:
        originals = get_original_device_names(device)
        print(f"   {device} -> {originals}")
    
    # 测试反向映射
    print("\n2. 测试原始名称到显示名称的转换:")
    original_names = ['EOL1', 'EOL2', 'EOL3', 'EOL4', 'EOL5', 'EOL#1', 'EOL#4']
    for original in original_names:
        display = get_display_device_name(original)
        print(f"   {original} -> {display}")
    
    # 测试SQL条件生成
    print("\n3. 测试SQL条件生成:")
    for station in ['SOC', 'VIU']:
        condition = get_station_condition_sql(station)
        print(f"   {station}: {condition}")
    
    # 测试文件名匹配逻辑
    print("\n4. 测试文件名匹配逻辑:")
    import re
    
    # 测试传统文件名
    traditional_file = "00003.0.01.10.007182LP881A0E9_20250804182519.csv"
    serial_number = "00003.0.01.10.007182LP881A0E9"
    pattern = rf'{re.escape(serial_number)}_(\d{{14}})\.csv$'
    match = re.search(pattern, traditional_file)
    print(f"   传统文件匹配: {traditional_file} -> {match.group(1) if match else 'No match'}")
    
    # 测试VIU文件名
    viu_file = "00003.0.01.10.007172LP884A1F9_2_20250804182519.csv"
    viu_serial = "00003.0.01.10.007172LP884A1F9"
    channel = "2"
    viu_pattern = rf'{re.escape(viu_serial)}_{channel}_(\d{{14}})\.csv$'
    viu_match = re.search(viu_pattern, viu_file)
    print(f"   VIU文件匹配: {viu_file} -> {viu_match.group(1) if viu_match else 'No match'}")

def test_log_detail_logic():
    """测试log详情逻辑"""
    print("\n🔍 测试log详情逻辑...")
    
    # 模拟不同设备的文件名生成
    serial_number = "00003.0.01.10.007172LP884A1F9"
    timestamp = "20250804182519"
    
    print("\n文件名格式测试:")
    
    # 传统设备
    traditional_filename = f"{serial_number}_{timestamp}.csv"
    print(f"   传统设备: {traditional_filename}")
    
    # VIU设备
    for channel in ['1', '2', '3']:
        viu_filename = f"{serial_number}_{channel}_{timestamp}.csv"
        print(f"   VIU_{channel}: {viu_filename}")

if __name__ == '__main__':
    test_device_mapping_fixes()
    test_log_detail_logic()
