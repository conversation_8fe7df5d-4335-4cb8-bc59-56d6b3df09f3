#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的打包脚本
"""

import os
import sys
import subprocess
import shutil

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除: {dir_name}")

def install_dependencies():
    """安装依赖"""
    print("📦 检查并安装依赖...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True, text=True)
        print("   依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"   依赖安装失败: {e}")
        return False
    return True

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")
    try:
        # 使用spec文件构建
        cmd = [sys.executable, '-m', 'PyInstaller', 'TestDataAnalysisSystem.spec', '--clean']
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("   构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   构建失败: {e}")
        print(f"   错误输出: {e.stderr}")
        return False

def copy_additional_files():
    """复制额外文件"""
    print("📁 复制额外文件...")
    files_to_copy = [
        'version.json',
        'access_log.txt'
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            try:
                shutil.copy2(file_name, 'dist/')
                print(f"   复制: {file_name}")
            except Exception as e:
                print(f"   复制失败 {file_name}: {e}")

def main():
    """主函数"""
    print("🚀 开始改进的打包流程...")
    
    # 1. 清理构建目录
    clean_build()
    
    # 2. 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，退出")
        return
    
    # 3. 构建exe
    if not build_exe():
        print("❌ 构建失败，退出")
        return
    
    # 4. 复制额外文件
    copy_additional_files()
    
    print("✅ 打包完成!")
    print("📂 输出目录: dist/")

if __name__ == '__main__':
    main()
