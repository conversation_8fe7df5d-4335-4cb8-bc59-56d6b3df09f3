('C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\build\\TestDataAnalysisSystem\\PYZ-00.pyz',
 [('__future__', 'C:\\Python3.11.9-64\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python3.11.9-64\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Python3.11.9-64\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle', 'C:\\Python3.11.9-64\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'C:\\Python3.11.9-64\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python3.11.9-64\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python3.11.9-64\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python3.11.9-64\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Python3.11.9-64\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('ai_analysis',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\ai_analysis.py',
   'PYMODULE'),
  ('ai_config',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\ai_config.py',
   'PYMODULE'),
  ('argparse', 'C:\\Python3.11.9-64\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Python3.11.9-64\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'C:\\Python3.11.9-64\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks', 'C:\\Python3.11.9-64\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'C:\\Python3.11.9-64\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python3.11.9-64\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python3.11.9-64\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'C:\\Python3.11.9-64\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Python3.11.9-64\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Python3.11.9-64\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2', 'C:\\Python3.11.9-64\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Python3.11.9-64\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'C:\\Python3.11.9-64\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Python3.11.9-64\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python3.11.9-64\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Python3.11.9-64\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python3.11.9-64\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python3.11.9-64\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python3.11.9-64\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python3.11.9-64\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python3.11.9-64\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python3.11.9-64\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python3.11.9-64\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Python3.11.9-64\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Python3.11.9-64\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Python3.11.9-64\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'C:\\Python3.11.9-64\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Python3.11.9-64\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses', 'C:\\Python3.11.9-64\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Python3.11.9-64\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'C:\\Python3.11.9-64\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Python3.11.9-64\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Python3.11.9-64\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\Python3.11.9-64\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'C:\\Python3.11.9-64\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python3.11.9-64\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python3.11.9-64\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Python3.11.9-64\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Python3.11.9-64\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Python3.11.9-64\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset', 'C:\\Python3.11.9-64\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python3.11.9-64\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Python3.11.9-64\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors', 'C:\\Python3.11.9-64\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'C:\\Python3.11.9-64\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Python3.11.9-64\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python3.11.9-64\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python3.11.9-64\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Python3.11.9-64\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message', 'C:\\Python3.11.9-64\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python3.11.9-64\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python3.11.9-64\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'C:\\Python3.11.9-64\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'C:\\Python3.11.9-64\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fileinput', 'C:\\Python3.11.9-64\\Lib\\fileinput.py', 'PYMODULE'),
  ('flask',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.sansio.app',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Python3.11.9-64\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Python3.11.9-64\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python3.11.9-64\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Python3.11.9-64\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Python3.11.9-64\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Python3.11.9-64\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Python3.11.9-64\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Python3.11.9-64\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python3.11.9-64\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Python3.11.9-64\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Python3.11.9-64\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python3.11.9-64\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'C:\\Python3.11.9-64\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Python3.11.9-64\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\Python3.11.9-64\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies', 'C:\\Python3.11.9-64\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'C:\\Python3.11.9-64\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'C:\\Python3.11.9-64\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc',
   'C:\\Python3.11.9-64\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python3.11.9-64\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python3.11.9-64\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Python3.11.9-64\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python3.11.9-64\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python3.11.9-64\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python3.11.9-64\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python3.11.9-64\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python3.11.9-64\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python3.11.9-64\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python3.11.9-64\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python3.11.9-64\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Python3.11.9-64\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python3.11.9-64\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python3.11.9-64\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python3.11.9-64\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python3.11.9-64\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Python3.11.9-64\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python3.11.9-64\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python3.11.9-64\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Python3.11.9-64\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Python3.11.9-64\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python3.11.9-64\\Lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'C:\\Python3.11.9-64\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python3.11.9-64\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python3.11.9-64\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python3.11.9-64\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\Python3.11.9-64\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'C:\\Python3.11.9-64\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Python3.11.9-64\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python3.11.9-64\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Python3.11.9-64\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python3.11.9-64\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Python3.11.9-64\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Python3.11.9-64\\Lib\\opcode.py', 'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Python3.11.9-64\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'C:\\Python3.11.9-64\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Python3.11.9-64\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'C:\\Python3.11.9-64\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Python3.11.9-64\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Python3.11.9-64\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Python3.11.9-64\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python3.11.9-64\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python3.11.9-64\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Python3.11.9-64\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python3.11.9-64\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pymysql',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql._auth',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.charset',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.connections',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.constants',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.converters',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.times',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'C:\\Python3.11.9-64\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Python3.11.9-64\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Python3.11.9-64\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter', 'C:\\Python3.11.9-64\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'C:\\Python3.11.9-64\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Python3.11.9-64\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Python3.11.9-64\\Lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'C:\\Python3.11.9-64\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Python3.11.9-64\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Python3.11.9-64\\Lib\\signal.py', 'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'C:\\Python3.11.9-64\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python3.11.9-64\\Lib\\socketserver.py', 'PYMODULE'),
  ('sqlite3', 'C:\\Python3.11.9-64\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Python3.11.9-64\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump', 'C:\\Python3.11.9-64\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'C:\\Python3.11.9-64\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Python3.11.9-64\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Python3.11.9-64\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python3.11.9-64\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python3.11.9-64\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Python3.11.9-64\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python3.11.9-64\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python3.11.9-64\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python3.11.9-64\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Python3.11.9-64\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Python3.11.9-64\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python3.11.9-64\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python3.11.9-64\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'C:\\Python3.11.9-64\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Python3.11.9-64\\Lib\\typing.py', 'PYMODULE'),
  ('unittest', 'C:\\Python3.11.9-64\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python3.11.9-64\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python3.11.9-64\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'C:\\Python3.11.9-64\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader',
   'C:\\Python3.11.9-64\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main', 'C:\\Python3.11.9-64\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.result',
   'C:\\Python3.11.9-64\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Python3.11.9-64\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Python3.11.9-64\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Python3.11.9-64\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util', 'C:\\Python3.11.9-64\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'C:\\Python3.11.9-64\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python3.11.9-64\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python3.11.9-64\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request',
   'C:\\Python3.11.9-64\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Python3.11.9-64\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'C:\\Python3.11.9-64\\Lib\\uuid.py', 'PYMODULE'),
  ('version_manager',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\version_manager.py',
   'PYMODULE'),
  ('webbrowser', 'C:\\Python3.11.9-64\\Lib\\webbrowser.py', 'PYMODULE'),
  ('werkzeug',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\2.WebServers\\.venv\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('xml', 'C:\\Python3.11.9-64\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'C:\\Python3.11.9-64\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Python3.11.9-64\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Python3.11.9-64\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Python3.11.9-64\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Python3.11.9-64\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Python3.11.9-64\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Python3.11.9-64\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Python3.11.9-64\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Python3.11.9-64\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python3.11.9-64\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python3.11.9-64\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python3.11.9-64\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python3.11.9-64\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Python3.11.9-64\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python3.11.9-64\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'C:\\Python3.11.9-64\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python3.11.9-64\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python3.11.9-64\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Python3.11.9-64\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Python3.11.9-64\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python3.11.9-64\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Python3.11.9-64\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python3.11.9-64\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python3.11.9-64\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'C:\\Python3.11.9-64\\Lib\\zipimport.py', 'PYMODULE')])
