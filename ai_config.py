#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析配置文件
"""

import os
import json
from datetime import datetime, timedelta

# AI服务配置 - 固定使用DeepSeek
AI_PROVIDERS = {
    'deepseek': {
        'name': 'DeepSeek AI',
        'api_url': 'https://api.deepseek.com/v1/chat/completions',
        'models': ['deepseek-chat', 'deepseek-reasoner'],
        'default_model': 'deepseek-reasoner',
        'max_tokens': 32000,  # deepseek-reasoner 支持更大的输出
        'temperature': 0.3,
        'fixed_api_key': '***********************************'
    }
}

# 默认配置 - 固定DeepSeek + 客户端调用
DEFAULT_CONFIG = {
    'provider': 'deepseek',
    'model': 'deepseek-reasoner',
    'api_key': '***********************************',
    'max_tokens': 32000,  # deepseek-reasoner 支持更大的输出
    'temperature': 0.3,
    'timeout': 60,  # 增加超时时间
    'cache_enabled': True,
    'cache_duration_hours': 1,
    'max_retries': 3,
    'retry_delay': 2,  # 增加重试间隔
    'client_side_ai': True  # 默认启用客户端调用
}

class AIConfig:
    """AI配置管理类"""
    
    def __init__(self, config_file='ai_config.json'):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                merged_config = DEFAULT_CONFIG.copy()
                merged_config.update(config)
                return merged_config
            else:
                return DEFAULT_CONFIG.copy()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return DEFAULT_CONFIG.copy()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key, default=None):
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """设置配置项"""
        self.config[key] = value
        return self.save_config()
    
    def get_provider_config(self):
        """获取当前提供商配置"""
        provider = self.config.get('provider', 'deepseek')
        return AI_PROVIDERS.get(provider, AI_PROVIDERS['deepseek'])
    
    def get_api_key(self):
        """获取API密钥"""
        provider = self.config.get('provider', 'deepseek')
        provider_config = self.get_provider_config()

        # 如果提供商有固定的API密钥，直接使用
        if 'fixed_api_key' in provider_config:
            return provider_config['fixed_api_key']

        # 优先从环境变量获取
        env_key = f"{provider.upper()}_API_KEY"
        api_key = os.getenv(env_key)

        if not api_key:
            # 从配置文件获取
            api_key = self.config.get('api_key', '')

        return api_key
    
    def is_configured(self):
        """检查是否已配置"""
        api_key = self.get_api_key()
        return bool(api_key) or self.config.get('provider') == 'local'

# 全局配置实例
ai_config = AIConfig()

# 缓存管理
class AnalysisCache:
    """分析结果缓存管理"""
    
    def __init__(self, cache_file='ai_cache.json'):
        self.cache_file = cache_file
        self.cache = self._load_cache()
    
    def _load_cache(self):
        """加载缓存文件"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载缓存文件失败: {e}")
            return {}
    
    def _save_cache(self):
        """保存缓存文件"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存缓存文件失败: {e}")
            return False
    
    def _generate_cache_key(self, params):
        """生成缓存键"""
        # 将参数转换为字符串并排序，确保一致性
        sorted_params = sorted(params.items())
        return str(hash(str(sorted_params)))
    
    def get(self, params):
        """获取缓存结果"""
        if not ai_config.get('cache_enabled', True):
            return None
        
        cache_key = self._generate_cache_key(params)
        cached_item = self.cache.get(cache_key)
        
        if not cached_item:
            return None
        
        # 检查缓存是否过期
        cache_time = datetime.fromisoformat(cached_item['timestamp'])
        cache_duration = timedelta(hours=ai_config.get('cache_duration_hours', 1))
        
        if datetime.now() - cache_time > cache_duration:
            # 缓存过期，删除
            del self.cache[cache_key]
            self._save_cache()
            return None
        
        return cached_item['result']
    
    def set(self, params, result):
        """设置缓存结果"""
        if not ai_config.get('cache_enabled', True):
            return
        
        cache_key = self._generate_cache_key(params)
        self.cache[cache_key] = {
            'result': result,
            'timestamp': datetime.now().isoformat()
        }
        
        # 清理过期缓存
        self._cleanup_expired_cache()
        self._save_cache()
    
    def _cleanup_expired_cache(self):
        """清理过期缓存"""
        cache_duration = timedelta(hours=ai_config.get('cache_duration_hours', 1))
        current_time = datetime.now()
        
        expired_keys = []
        for key, item in self.cache.items():
            cache_time = datetime.fromisoformat(item['timestamp'])
            if current_time - cache_time > cache_duration:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
    
    def clear(self):
        """清空缓存"""
        self.cache = {}
        self._save_cache()

# 全局缓存实例
analysis_cache = AnalysisCache()

# 错误处理和重试机制
class AIError(Exception):
    """AI服务错误"""
    pass

class AIRateLimitError(AIError):
    """API速率限制错误"""
    pass

class AIQuotaExceededError(AIError):
    """API配额超限错误"""
    pass

class AITimeoutError(AIError):
    """API超时错误"""
    pass

def handle_ai_error(error, response=None):
    """处理AI服务错误"""
    if response:
        status_code = response.status_code
        
        if status_code == 429:
            raise AIRateLimitError("API调用频率超限，请稍后重试")
        elif status_code == 402:
            raise AIQuotaExceededError("API配额已用完，请检查账户余额")
        elif status_code == 401:
            raise AIError("API密钥无效，请检查配置")
        elif status_code == 503:
            raise AIError("AI服务暂时不可用，请稍后重试")
        else:
            raise AIError(f"AI服务错误 ({status_code}): {response.text}")
    else:
        if "timeout" in str(error).lower():
            raise AITimeoutError("AI服务响应超时")
        else:
            raise AIError(f"AI服务调用失败: {str(error)}")

# 分析提示模板
ANALYSIS_PROMPTS = {
    'system': """你是一名专业的汽车制造业测试数据分析专家，擅长EOL（End of Line）测试数据分析和质量改进建议。
你具有以下专业知识：
1. 汽车电子系统测试流程和标准
2. EOL测试中常见的失败模式和根因分析
3. 质量控制和持续改进方法
4. 统计分析和趋势识别

请用专业、准确、实用的语言提供分析建议。""",
    
    'analysis_template': """
请分析以下零跑汽车EOL测试数据，并提供专业的洞察和建议。

## 数据概览
- 分析周期: {period}
- 测试站位: {station}
- 总测试数: {total_tests}
- 通过测试: {passed_tests}
- 失败测试: {failed_tests}
- 整体失败率: {fail_rate:.2f}%

## Top {issue_count} 失败项分布
{top_issues}

## 最近趋势数据
{trends}

请从以下角度进行分析：

1. **关键问题识别**: 识别最需要关注的失败项，分析其影响程度
2. **趋势分析**: 分析失败率趋势，识别是否存在恶化或改善趋势
3. **根因分析**: 基于失败项名称和分布，推测可能的根本原因
4. **优先级建议**: 建议优先解决的问题及其理由
5. **改进建议**: 提供具体的质量改进建议和预防措施

请用中文回答，保持专业性和实用性。结果请用JSON格式返回，包含以下字段：
- summary: 总体分析摘要（100-200字）
- key_findings: 关键发现列表（3-5个要点）
- trend_analysis: 趋势分析（50-100字）
- root_cause_analysis: 根因分析（100-150字）
- priority_recommendations: 优先级建议（3-5个要点）
- improvement_suggestions: 改进建议（3-5个要点）
"""
}

def get_analysis_prompt(data):
    """生成分析提示"""
    top_issues_text = ""
    for i, issue in enumerate(data['top_issues'][:10], 1):
        top_issues_text += f"{i}. {issue['failure_item']}: {issue['count']}次 ({issue.get('percentage', 0):.1f}%)\n"
    
    trends_text = ""
    for trend in data['trends'][:7]:
        trends_text += f"- {trend['test_date']}: 失败率 {trend['daily_fail_rate']:.1f}% ({trend['failed_tests']}/{trend['total_tests']})\n"
    
    return ANALYSIS_PROMPTS['analysis_template'].format(
        period=data['summary']['analysis_period'],
        station=data['summary']['station'] or '全部',
        total_tests=data['statistics']['total_tests'],
        passed_tests=data['statistics']['passed_tests'],
        failed_tests=data['statistics']['failed_tests'],
        fail_rate=data['statistics']['fail_rate'],
        issue_count=len(data['top_issues']),
        top_issues=top_issues_text,
        trends=trends_text
    )
