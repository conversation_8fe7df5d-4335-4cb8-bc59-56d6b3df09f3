#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备名称映射功能
"""

import requests
import json

def test_device_mapping():
    """测试设备名称映射"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔍 测试设备名称映射功能...")
    
    # 1. 测试leap8155数据API
    print("\n1. 测试leap8155数据API:")
    try:
        response = requests.get(f"{base_url}/api/leap8155/data?time_range=0&test_station=SOC")
        if response.status_code == 200:
            data = response.json()
            print(f"   获取到 {len(data)} 条数据")
            if data:
                stations = set(item.get('Station', '') for item in data[:10])
                print(f"   前10条数据的设备名称: {stations}")
        else:
            print(f"   请求失败: {response.status_code}")
    except Exception as e:
        print(f"   请求异常: {e}")
    
    # 2. 测试VIU数据
    print("\n2. 测试VIU数据:")
    try:
        response = requests.get(f"{base_url}/api/leap8155/data?time_range=0&test_station=VIU")
        if response.status_code == 200:
            data = response.json()
            print(f"   获取到 {len(data)} 条数据")
            if data:
                stations = set(item.get('Station', '') for item in data[:10])
                print(f"   前10条数据的设备名称: {stations}")
        else:
            print(f"   请求失败: {response.status_code}")
    except Exception as e:
        print(f"   请求异常: {e}")
    
    # 3. 测试特定设备查询
    print("\n3. 测试特定设备查询:")
    devices_to_test = ['SOC_1', 'SOC_2', 'VIU', 'VIU_1']
    
    for device in devices_to_test:
        try:
            response = requests.get(f"{base_url}/api/leap8155/data?time_range=0&eol={device}")
            if response.status_code == 200:
                data = response.json()
                print(f"   {device}: 获取到 {len(data)} 条数据")
            else:
                print(f"   {device}: 请求失败 {response.status_code}")
        except Exception as e:
            print(f"   {device}: 请求异常 {e}")

def test_mapping_functions():
    """测试映射函数"""
    print("\n🔧 测试映射函数:")
    
    # 导入映射函数
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from app import get_display_device_name, get_original_device_names, get_station_condition_sql
    
    # 测试显示名称转换
    print("\n显示名称转换测试:")
    test_cases = ['EOL1', 'EOL2', 'EOL3', 'EOL4', 'EOL5', 'EOL#1', 'EOL#2']
    for original in test_cases:
        display = get_display_device_name(original)
        print(f"   {original} -> {display}")
    
    # 测试反向映射
    print("\n反向映射测试:")
    display_names = ['SOC_1', 'SOC_2', 'SOC_3', 'SOC_4', 'VIU', 'VIU_1', 'VIU_2', 'VIU_3']
    for display in display_names:
        originals = get_original_device_names(display)
        print(f"   {display} -> {originals}")
    
    # 测试SQL条件生成
    print("\nSQL条件生成测试:")
    for station in ['SOC', 'VIU']:
        condition = get_station_condition_sql(station)
        print(f"   {station}: {condition}")

if __name__ == '__main__':
    test_mapping_functions()
    test_device_mapping()
