#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发环境启动脚本 - Python 3.13兼容版本
"""

import os
import sys
from app import app

def main():
    """主函数"""
    print("🚀 启动测试数据分析系统...")
    print(f"🐍 Python版本: {sys.version}")
    
    # 设置Flask环境变量
    os.environ['FLASK_APP'] = 'app.py'
    os.environ['FLASK_ENV'] = 'development'
    
    # 检查Python版本
    if sys.version_info >= (3, 13):
        print("⚠️  检测到Python 3.13")
        print("💡 使用生产模式启动以避免调试器兼容性问题")
        print("🌐 服务将在 http://0.0.0.0:5000 启动")
        
        # 使用生产模式启动
        app.run(
            debug=False,
            host='0.0.0.0',
            port=5000,
            use_reloader=False,  # 禁用重载器
            threaded=True
        )
    else:
        print("✅ Python版本兼容，启用调试模式")
        print("🌐 服务将在 http://0.0.0.0:5000 启动")
        
        # 使用调试模式启动
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000
        )

if __name__ == '__main__':
    main()
