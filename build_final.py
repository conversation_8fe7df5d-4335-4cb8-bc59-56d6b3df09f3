#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据分析系统 - 最终打包脚本
功能：
1. 运行打包后的exe时候，读取和写入access_log，access_log路径放在dist文件夹外面
2. 打包成exe命名为TestDataAnalysisSystem
3. 打包exe的时候把版本信息json文件打包到dist文件夹内
"""

import os
import sys
import json
import subprocess
import shutil
from datetime import datetime

def increment_version():
    """递增版本号并更新版本信息"""
    version_file = 'version.json'

    # 读取当前版本信息
    if os.path.exists(version_file):
        with open(version_file, 'r', encoding='utf-8') as f:
            version_info = json.load(f)
    else:
        version_info = {
            "version": "1.0.0",
            "build_count": 0
        }

    # 递增构建计数
    build_count = version_info.get('build_count', 0) + 1

    # 解析当前版本号并递增
    current_version = version_info.get('version', '1.0.0')
    try:
        # 移除可能的 'v' 前缀
        if current_version.startswith('v'):
            current_version = current_version[1:]

        major, minor, patch = map(int, current_version.split('.'))

        # 递增补丁版本号
        patch += 1

        # 如果补丁版本号达到10，递增次版本号
        if patch >= 10:
            patch = 0
            minor += 1

            # 如果次版本号达到10，递增主版本号
            if minor >= 10:
                minor = 0
                major += 1

        new_version = f"{major}.{minor}.{patch}"
    except:
        # 如果解析失败，使用构建计数作为补丁版本
        new_version = f"1.0.{build_count}"

    # 更新版本信息
    version_info['version'] = new_version
    version_info['build_count'] = build_count
    version_info['build_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    version_info['last_build'] = datetime.now().isoformat()

    # 保存更新后的版本信息
    with open(version_file, 'w', encoding='utf-8') as f:
        json.dump(version_info, f, indent=2, ensure_ascii=False)

    print(f"✅ 版本信息已更新: {current_version} -> {new_version} (构建 #{build_count})")
    return version_info

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)

def build_exe():
    """使用PyInstaller打包exe"""
    try:
        print("🔨 开始打包exe文件...")

        # 检查必要文件是否存在
        required_files = ['app.py', 'static', 'version.json', 'ai_analysis.py', 'ai_config.py']
        missing_files = []
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
            return False

        # PyInstaller命令
        cmd = [
            'pyinstaller',
            '--onefile',
            '--add-data', 'static;static',
            '--add-data', 'version.json;.',
            '--add-data', 'ai_analysis.py;.',
            '--add-data', 'ai_config.py;.',
            '--name', 'TestDataAnalysisSystem',
            '--console',
            'app.py'
        ]

        print(f"📦 执行命令: {' '.join(cmd)}")
        
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

        if result.returncode == 0:
            print("✅ exe文件打包成功!")
            return True
        else:
            print("❌ exe文件打包失败!")
            print("错误信息:", result.stderr)
            if result.stdout:
                print("输出信息:", result.stdout)
            return False

    except Exception as e:
        print(f"❌ 打包过程出错: {str(e)}")
        return False

def copy_version_files():
    """复制版本信息文件到dist目录"""
    try:
        dist_dir = 'dist'
        if not os.path.exists(dist_dir):
            print("❌ dist目录不存在")
            return False
        
        version_files = ['version.json']
        for version_file in version_files:
            if os.path.exists(version_file):
                dest_path = os.path.join(dist_dir, version_file)
                shutil.copy2(version_file, dest_path)
                print(f"✅ 已复制 {version_file} 到 dist 目录")
            else:
                print(f"⚠️  版本文件不存在: {version_file}")
        
        return True
    except Exception as e:
        print(f"❌ 复制版本文件失败: {str(e)}")
        return False

def create_access_log_readme():
    """在项目根目录创建access_log说明文件"""
    try:
        readme_content = """# Access Log 说明

## 日志文件位置
- 开发环境：项目根目录下的 `access_log.txt`
- 生产环境：exe文件所在目录的父目录下的 `access_log.txt`

## 目录结构示例
```
项目根目录/
├── dist/
│   └── TestDataAnalysisSystem.exe
├── access_log.txt  ← 日志文件位置
└── 其他文件...
```

## 说明
这样设计的好处是：
1. 替换 dist 文件夹不会影响 access_log 内容
2. 日志文件持久保存在项目根目录
3. 便于日志文件的备份和管理

## 注意事项
- 首次运行exe时，如果access_log.txt不存在，会自动创建
- 日志文件采用UTF-8编码
- 每次访问都会追加记录到文件末尾
"""
        
        with open('ACCESS_LOG_README.md', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✅ 已创建 ACCESS_LOG_README.md 说明文件")
        return True
    except Exception as e:
        print(f"❌ 创建说明文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 测试数据分析系统 - 最终打包工具")
    print("=" * 60)
    
    # 步骤1: 清理构建目录
    print("\n📋 步骤1: 清理构建目录")
    clean_build_dirs()
    
    # 步骤2: 更新版本信息
    print("\n📋 步骤2: 更新版本信息")
    version_info = increment_version()
    
    # 步骤3: 打包exe
    print(f"\n📋 步骤3: 打包exe文件 (版本: {version_info['version']})")
    success = build_exe()
    
    if not success:
        print("\n❌ 打包失败，请检查错误信息")
        return
    
    # 步骤4: 复制版本文件到dist目录
    print("\n📋 步骤4: 复制版本文件到dist目录")
    copy_version_files()
    
    # 步骤5: 创建说明文件
    print("\n📋 步骤5: 创建access_log说明文件")
    create_access_log_readme()
    
    # 显示结果
    print("\n" + "=" * 60)
    print("🎉 打包完成!")
    print(f"📦 当前版本: {version_info['version']}")
    print(f"🕒 构建时间: {version_info['build_time']}")
    print(f"📁 exe文件位置: dist/TestDataAnalysisSystem.exe")
    print(f"📝 日志文件位置: access_log.txt (在项目根目录)")
    print("=" * 60)
    
    # 显示使用说明
    print("\n📖 使用说明:")
    print("1. 将整个项目文件夹部署到目标环境")
    print("2. 运行 dist/TestDataAnalysisSystem.exe")
    print("3. access_log.txt 会在项目根目录自动创建")
    print("4. 替换 dist 文件夹不会影响日志文件")

if __name__ == '__main__':
    main()
