#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python 3.13 专用打包脚本
"""

import os
import sys
import subprocess
import shutil

def install_dependencies():
    """安装Python 3.13兼容的依赖"""
    print("📦 安装Python 3.13兼容的依赖...")
    
    # 基础包
    basic_packages = [
        'Flask>=3.0.0',
        'PyMySQL>=1.1.0', 
        'requests>=2.31.0',
        'pyinstaller>=6.0.0'
    ]
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade'] + basic_packages, 
                      check=True)
        print("✅ 基础包安装成功")
    except subprocess.CalledProcessError as e:
        print(f"❌ 基础包安装失败: {e}")
        return False
    
    # 尝试安装科学计算包
    try:
        print("🔬 安装科学计算包...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--pre', 'numpy', 'pandas'], 
                      check=True)
        print("✅ 科学计算包安装成功")
    except subprocess.CalledProcessError as e:
        print(f"⚠️  科学计算包安装失败: {e}")
        print("💡 尝试使用二进制包...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--only-binary=all', 'numpy', 'pandas'], 
                          check=True)
            print("✅ 二进制包安装成功")
        except subprocess.CalledProcessError:
            print("❌ 无法安装pandas和numpy，请考虑使用Python 3.11")
            return False
    
    return True

def build_exe():
    """构建exe"""
    print("🔨 开始构建exe...")
    
    # 清理
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
    
    # 构建命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--clean',
        '--noconfirm',
        '--add-data', 'static;static',
        '--add-data', 'version.json;.',
        '--hidden-import', 'pymysql',
        '--hidden-import', 'pymysql.cursors',
        '--hidden-import', 'ai_analysis',
        '--exclude-module', 'tkinter',
        '--exclude-module', 'matplotlib',
        '--name', 'TestDataAnalysisSystem',
        'app.py'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功!")
        
        # 复制额外文件
        if os.path.exists('access_log.txt'):
            shutil.copy2('access_log.txt', 'dist/')
            
        return True
    except subprocess.CalledProcessError as e:
        print("❌ 构建失败!")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🚀 Python 3.13 打包流程开始...")
    print(f"🐍 Python版本: {sys.version}")
    
    if not install_dependencies():
        print("❌ 依赖安装失败，退出")
        return
    
    if build_exe():
        print("✅ 打包完成!")
        print("📂 输出目录: dist/")
    else:
        print("❌ 打包失败!")

if __name__ == '__main__':
    main()
