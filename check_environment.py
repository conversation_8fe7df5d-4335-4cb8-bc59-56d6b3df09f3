#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查脚本
"""

import sys
import subprocess
import pkg_resources

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 13:
        print("⚠️  警告: Python 3.13+ 可能与某些包不兼容")
        print("💡 建议: 使用Python 3.11或3.12")
        return False
    elif version.major == 3 and 8 <= version.minor <= 12:
        print("✅ Python版本兼容")
        return True
    else:
        print("❌ Python版本不支持")
        return False

def check_packages():
    """检查已安装的包"""
    required_packages = [
        'flask', 'pymysql', 'pandas', 'numpy', 'requests', 'pyinstaller'
    ]
    
    print("\n📦 检查已安装的包:")
    missing_packages = []
    
    for package in required_packages:
        try:
            version = pkg_resources.get_distribution(package).version
            print(f"✅ {package}: {version}")
        except pkg_resources.DistributionNotFound:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    return missing_packages

def install_compatible_packages():
    """安装兼容的包"""
    print("\n🔧 尝试安装兼容的包...")
    
    # 基础包
    basic_packages = ['Flask', 'PyMySQL', 'requests', 'pyinstaller']
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade'] + basic_packages, 
                      check=True)
        print("✅ 基础包安装成功")
    except subprocess.CalledProcessError:
        print("❌ 基础包安装失败")
        return False
    
    # 尝试安装numpy和pandas
    try:
        print("🔄 尝试安装numpy和pandas...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--pre', 'numpy', 'pandas'], 
                      check=True)
        print("✅ numpy和pandas安装成功")
    except subprocess.CalledProcessError:
        print("❌ numpy和pandas安装失败")
        print("💡 建议: 降级到Python 3.11或3.12")
        return False
    
    return True

def main():
    """主函数"""
    print("🔍 环境检查开始...")
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查包
    missing = check_packages()
    
    if missing:
        print(f"\n❌ 缺少包: {', '.join(missing)}")
        
        if python_ok:
            print("🔧 尝试自动安装...")
            if install_compatible_packages():
                print("✅ 环境准备完成!")
            else:
                print("❌ 自动安装失败")
        else:
            print("💡 请先解决Python版本问题")
    else:
        print("\n✅ 所有依赖都已安装!")
    
    print("\n📋 建议的打包命令:")
    print("pyinstaller --onefile --add-data \"static;static\" --name TestDataAnalysisSystem app.py")

if __name__ == '__main__':
    main()
