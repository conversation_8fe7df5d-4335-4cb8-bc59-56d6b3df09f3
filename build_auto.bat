@echo off
chcp 65001 >nul
echo 🚀 自动版本管理打包脚本...

echo.
echo 📋 当前版本信息:
python version_manager.py

echo.
echo 🧹 清理构建目录...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__

echo.
echo 🔢 更新版本号...
python -c "from version_manager import increment_version; increment_version()"

echo.
echo 🔨 开始打包...
python -m PyInstaller --onefile --clean --noconfirm --add-data "static;static" --add-data "version.json;." --name TestDataAnalysisSystem app.py

if %errorlevel% equ 0 (
    echo.
    echo ✅ 打包成功!
    
    echo.
    echo 📁 复制额外文件...
    if exist version.json copy version.json dist\ >nul
    if exist access_log.txt copy access_log.txt dist\ >nul
    
    echo.
    echo 📄 检查输出文件...
    if exist dist\TestDataAnalysisSystem.exe (
        for %%I in (dist\TestDataAnalysisSystem.exe) do echo    ✅ TestDataAnalysisSystem.exe - %%~zI bytes
    ) else (
        echo    ❌ 未找到exe文件
    )
    
    if exist dist\version.json (
        echo    ✅ version.json 已复制
    ) else (
        echo    ❌ version.json 未找到
    )
    
    echo.
    echo 📋 最终版本信息:
    python -c "from version_manager import get_version_info; info=get_version_info(); print(f'   版本: {info[\"version\"]}'); print(f'   构建时间: {info[\"build_time\"]}'); print(f'   构建次数: {info[\"build_count\"]}')"
    
    echo.
    echo 🎉 打包完成! 输出目录: dist\
) else (
    echo.
    echo ❌ 打包失败!
)

echo.
pause
