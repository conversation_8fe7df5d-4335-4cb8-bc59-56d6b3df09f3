<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>烟花效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #000;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        
        canvas {
            display: block;
            background: linear-gradient(to bottom, #000428, #004e92);
        }
        
        .info {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            text-align: center;
            z-index: 10;
        }
    </style>
</head>
<body>
    <canvas id="fireworksCanvas"></canvas>
    <div class="info">
        点击屏幕任意位置放烟花 | 按空格键连续放烟花
    </div>

    <script>
        const canvas = document.getElementById('fireworksCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置画布大小
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // 烟花粒子类
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 12;
                this.vy = (Math.random() - 0.5) * 12;
                this.life = 1;
                this.decay = Math.random() * 0.02 + 0.01;
                this.color = color;
                this.size = Math.random() * 4 + 2;
                this.gravity = 0.1;
                this.friction = 0.98;
            }
            
            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.vy += this.gravity;
                this.vx *= this.friction;
                this.vy *= this.friction;
                this.life -= this.decay;
                this.size *= 0.99;
            }
            
            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.shadowBlur = 15;
                ctx.shadowColor = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }
        
        // 烟花类
        class Firework {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.particles = [];
                this.colors = [
                    '#ff0043', '#14fc56', '#1e7fff', '#e60aff', 
                    '#ffbf36', '#ffffff', '#ff6b6b', '#4ecdc4',
                    '#45b7d1', '#f39c12', '#e74c3c', '#9b59b6'
                ];
                this.explode();
            }
            
            explode() {
                const particleCount = Math.random() * 50 + 30;
                const color = this.colors[Math.floor(Math.random() * this.colors.length)];
                
                for (let i = 0; i < particleCount; i++) {
                    this.particles.push(new Particle(this.x, this.y, color));
                }
                
                // 添加一些白色闪光粒子
                for (let i = 0; i < 10; i++) {
                    this.particles.push(new Particle(this.x, this.y, '#ffffff'));
                }
            }
            
            update() {
                this.particles = this.particles.filter(particle => {
                    particle.update();
                    return particle.life > 0 && particle.size > 0.5;
                });
            }
            
            draw() {
                this.particles.forEach(particle => particle.draw());
            }
            
            isDead() {
                return this.particles.length === 0;
            }
        }
        
        // 烟花管理
        let fireworks = [];
        let lastAutoFirework = 0;
        
        // 创建烟花
        function createFirework(x, y) {
            fireworks.push(new Firework(x, y));
        }
        
        // 动画循环
        function animate() {
            // 创建拖尾效果
            ctx.fillStyle = 'rgba(0, 4, 40, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 自动创建烟花
            const now = Date.now();
            if (now - lastAutoFirework > 1500 + Math.random() * 2000) {
                createFirework(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height * 0.7 + 50
                );
                lastAutoFirework = now;
            }
            
            // 更新和绘制烟花
            fireworks = fireworks.filter(firework => {
                firework.update();
                firework.draw();
                return !firework.isDead();
            });
            
            requestAnimationFrame(animate);
        }
        
        // 鼠标点击事件
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            createFirework(x, y);
        });
        
        // 键盘事件
        let spacePressed = false;
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && !spacePressed) {
                spacePressed = true;
                e.preventDefault();
                
                // 连续放烟花
                const interval = setInterval(() => {
                    createFirework(
                        Math.random() * canvas.width,
                        Math.random() * canvas.height * 0.7 + 50
                    );
                }, 100);
                
                // 停止连续放烟花
                const stopInterval = () => {
                    clearInterval(interval);
                    document.removeEventListener('keyup', stopInterval);
                };
                document.addEventListener('keyup', stopInterval);
            }
        });
        
        document.addEventListener('keyup', (e) => {
            if (e.code === 'Space') {
                spacePressed = false;
            }
        });
        
        // 开始动画
        animate();
        
        // 初始烟花
        setTimeout(() => {
            createFirework(canvas.width / 2, canvas.height / 2);
        }, 500);
    </script>
</body>
</html>
