#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本管理器 - 自动累加版本号
"""

import json
import os
from datetime import datetime

VERSION_FILE = 'version.json'

def get_current_version():
    """获取当前版本号"""
    if os.path.exists(VERSION_FILE):
        try:
            with open(VERSION_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('version', '1.0.0'), data.get('build_time', ''), data.get('build_count', 0)
        except:
            pass

    # 默认版本
    return '1.0.0', '', 0

def increment_version():
    """递增版本号"""
    current_version, _, build_count = get_current_version()

    # 解析版本号
    try:
        major, minor, patch = map(int, current_version.split('.'))
    except:
        major, minor, patch = 1, 0, 0

    # 递增补丁版本号
    patch += 1

    # 如果补丁版本号达到10，递增次版本号
    if patch >= 10:
        patch = 0
        minor += 1

        # 如果次版本号达到10，递增主版本号
        if minor >= 10:
            minor = 0
            major += 1

    new_version = f"{major}.{minor}.{patch}"
    build_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    build_count += 1
    
    # 保存新版本信息
    version_data = {
        'version': new_version,
        'build_time': build_time,
        'build_count': build_count,
        'last_build': datetime.now().isoformat()
    }
    
    with open(VERSION_FILE, 'w', encoding='utf-8') as f:
        json.dump(version_data, f, indent=2, ensure_ascii=False)
    
    print(f"🔢 版本号已更新: {current_version} -> {new_version}")
    print(f"📅 构建时间: {build_time}")
    print(f"🔨 构建次数: {build_count}")
    
    return new_version, build_time, build_count

def get_version_info():
    """获取版本信息用于显示"""
    version, build_time, build_count = get_current_version()

    if build_time and build_time != '':
        return {
            'version': f"v{version}",
            'build_time': build_time,
            'build_count': build_count,
            'display_text': f"v{version}"
        }
    else:
        # 如果没有构建时间，使用当前时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        return {
            'version': f"v{version}",
            'build_time': current_time,
            'build_count': build_count if build_count > 0 else 1,
            'display_text': f"v{version}"
        }

if __name__ == "__main__":
    # 测试版本管理
    print("当前版本信息:")
    current = get_version_info()
    print(f"版本: {current['version']}")
    print(f"构建时间: {current['build_time']}")
    print(f"构建次数: {current['build_count']}")
    
    print("\n递增版本号:")
    increment_version()
