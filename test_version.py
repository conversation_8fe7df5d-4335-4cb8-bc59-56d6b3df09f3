#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试版本管理功能
"""

import json
import os
from version_manager import get_current_version, increment_version, get_version_info

def test_version_functions():
    """测试版本管理功能"""
    print("🔍 测试版本管理功能...")
    
    # 1. 获取当前版本
    print("\n1. 获取当前版本:")
    current_version, build_time, build_count = get_current_version()
    print(f"   版本: {current_version}")
    print(f"   构建时间: {build_time}")
    print(f"   构建次数: {build_count}")
    
    # 2. 获取版本信息
    print("\n2. 获取版本信息:")
    version_info = get_version_info()
    print(f"   版本: {version_info['version']}")
    print(f"   构建时间: {version_info['build_time']}")
    print(f"   构建次数: {version_info['build_count']}")
    print(f"   显示文本: {version_info['display_text']}")
    
    # 3. 读取version.json文件
    print("\n3. 直接读取version.json:")
    if os.path.exists('version.json'):
        with open('version.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            print(f"   文件内容: {data}")
    else:
        print("   version.json文件不存在")
    
    # 4. 测试递增版本
    print("\n4. 测试递增版本:")
    try:
        new_version, new_build_time, new_build_count = increment_version()
        print(f"   新版本: {new_version}")
        print(f"   新构建时间: {new_build_time}")
        print(f"   新构建次数: {new_build_count}")
        
        # 验证文件是否更新
        print("\n5. 验证文件更新:")
        if os.path.exists('version.json'):
            with open('version.json', 'r', encoding='utf-8') as f:
                updated_data = json.load(f)
                print(f"   更新后文件内容: {updated_data}")
        
    except Exception as e:
        print(f"   递增版本失败: {e}")

if __name__ == '__main__':
    test_version_functions()
