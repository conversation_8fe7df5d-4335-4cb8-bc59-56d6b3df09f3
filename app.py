from flask import Flask, request, jsonify, Response, send_from_directory
import pymysql
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import re
import os
import fnmatch
import json
from flask import stream_with_context
import traceback
import urllib.parse
import pymysql.cursors
import sys
import requests
from ai_analysis import AIAnalyzer

# 获取exe文件所在目录
def get_exe_dir():
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        return os.path.dirname(sys.executable)
    else:
        # 如果是Python脚本
        return os.path.dirname(os.path.abspath(__file__))

# 获取access_log文件路径（放在dist文件夹外面）
def get_access_log_path():
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件，access_log放在exe文件的父目录的父目录（项目根目录）
        exe_dir = os.path.dirname(sys.executable)  # dist目录
        parent_dir = os.path.dirname(exe_dir)      # 项目根目录
        return os.path.join(parent_dir, 'access_log.txt')
    else:
        # 如果是Python脚本，放在当前目录
        return os.path.join(os.path.dirname(os.path.abspath(__file__)), 'access_log.txt')

# 访问日志记录函数
def log_access(ip, user_agent, path, method):
    try:
        log_file = get_access_log_path()

        # 确保日志文件的目录存在
        log_dir = os.path.dirname(log_file)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] IP: {ip} | Method: {method} | Path: {path} | User-Agent: {user_agent}\n"

        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
    except Exception as e:
        print(f"记录访问日志失败: {e}")

app = Flask(__name__, static_url_path='', static_folder='static')

# 添加访问日志中间件
@app.before_request
def before_request():
    # 获取客户端IP地址
    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
        ip = request.environ['REMOTE_ADDR']
    else:
        ip = request.environ['HTTP_X_FORWARDED_FOR']

    # 获取User-Agent
    user_agent = request.headers.get('User-Agent', 'Unknown')

    # 记录访问日志
    log_access(ip, user_agent, request.path, request.method)

# 数据库配置保持不变
db_config = {
    'host': '***********',
    'user': 'root',
    'password': 'taomao128',  
    'database': 'mydatabase',
    'port': 3306
}

# 数据库连接函数修改
def get_db_connection():
    try:
        conn = pymysql.connect(
            host='***********',
            user='root',
            password='taomao128',
            database='mydatabase',
            port=3306,
            charset='utf8mb4'
        )
        return conn
    except Exception as err:
        print(f"数据库连接错误: {err}")
        return None

# 日期过滤函数修改
def get_date_filter(time_range, custom_date=None):
    now = datetime.now()
    
    if custom_date:
        start_date = datetime.strptime(custom_date, '%Y-%m-%d')
        end_date = start_date + timedelta(days=1)
    else:
        if time_range == '0':  # 今天
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = now
        elif time_range == '1':  # 昨天
            start_date = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = (now - timedelta(days=1)).replace(hour=23, minute=59, second=59, microsecond=999999)
        elif time_range == '7':  # 最近7天
            start_date = now - timedelta(days=7)
            end_date = now
        elif time_range == '30':  # 最近30天
            start_date = now - timedelta(days=30)
            end_date = now
        else:  # 默认最近7天
            start_date = now - timedelta(days=7)
            end_date = now
    
    return start_date, end_date

@app.route('/static/<path:path>')
def send_static(path):
    return send_from_directory('static', path)

@app.route('/')
def index():
    return app.send_static_file('index.html')

@app.route('/debug_ai.html')
def debug_ai():
    return send_from_directory('.', 'debug_ai.html')

@app.route('/test_frontend.html')
def test_frontend():
    return send_from_directory('.', 'test_frontend.html')

# API路由函数修改
# 修改get_data路由中的数据处理部分
@app.route('/api/data')
def get_data():
    try:
        time_range = request.args.get('time_range', '7')
        station = request.args.get('station', 'AP5')
        selected_slave = request.args.get('slave', '')
        custom_date = request.args.get('custom_date')
        custom_date_start = request.args.get('custom_date_start')
        custom_date_end = request.args.get('custom_date_end')

        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)

        # 优先使用精确的自定义时间范围
        if custom_date_start and custom_date_end:
            start_date = datetime.strptime(custom_date_start, '%Y-%m-%dT%H:%M')
            end_date = datetime.strptime(custom_date_end, '%Y-%m-%dT%H:%M')
        else:
            start_date, end_date = get_date_filter(time_range, custom_date)

        base_query = """
            SELECT 
                StartTestTime,
                EndTestTime,
                Serial_Number,
                Customer_Serial_Number,
                Slave,
                Slot,
                TestResult,
                Station,
                Type,
                CycleTime
            FROM log_entries 
            WHERE StartTestTime >= %s AND StartTestTime <= %s
            AND Station = %s
        """
        params = [start_date, end_date, station]
        
        if selected_slave and station == 'AP5':
            base_query += " AND Slave = %s"
            params.append(selected_slave)
            
        base_query += " ORDER BY StartTestTime DESC"
        
        cursor.execute(base_query, params)
        results = cursor.fetchall()
        
        # 将结果转换为DataFrame
        df = pd.DataFrame(results)
        
        if len(df) == 0:
            return jsonify({
                'slave_distribution': [],
                'slot_distribution': [],
                'test_results': {
                    'total': 0,
                    'pass': 0,
                    'fail': 0
                },
                'ct_stats': {
                    'min': 0,
                    'max': 0,
                    'avg': 0,
                    'total': 0
                },
                'recent_data': []
            })

        # 首先处理日期列的空值
        df['StartTestTime'] = pd.to_datetime(df['StartTestTime'])
        df['EndTestTime'] = pd.to_datetime(df['EndTestTime'])
        
        # 将NaT替换为None
        df = df.replace({pd.NaT: None})

        # Slave分布计算
        slave_stats = df.groupby(['Slave', 'TestResult']).size().unstack(fill_value=0)
        slave_distribution = []
        for slave in slave_stats.index:
            total = slave_stats.loc[slave].sum()
            passed = slave_stats.loc[slave, 'PASSED'] if 'PASSED' in slave_stats.columns else 0
            failed = slave_stats.loc[slave, 'FAILED'] if 'FAILED' in slave_stats.columns else 0
            fail_rate = (failed / total * 100) if total > 0 else 0
            slave_distribution.append({
                'name': slave,
                'passed': int(passed),
                'failed': int(failed),
                'failRate': round(fail_rate, 2)
            })

        # Slot分布计算
        slot_stats = df.groupby(['Slot', 'TestResult']).size().unstack(fill_value=0)
        slot_distribution = []
        for slot in slot_stats.index:
            total = slot_stats.loc[slot].sum()
            passed = slot_stats.loc[slot, 'PASSED'] if 'PASSED' in slot_stats.columns else 0
            failed = slot_stats.loc[slot, 'FAILED'] if 'FAILED' in slot_stats.columns else 0
            fail_rate = (failed / total * 100) if total > 0 else 0
            slot_distribution.append({
                'name': slot,
                'passed': int(passed),
                'failed': int(failed),
                'failRate': round(fail_rate, 2)
            })

        # 测试结果统计
        test_results = {
            'total': len(df),
            'pass': len(df[df['TestResult'] == 'PASSED']),
            'fail': len(df[df['TestResult'] == 'FAILED'])
        }

        # CT统计计算
        ct_stats = {
            'min': float(df['CycleTime'].min()),
            'max': float(df['CycleTime'].max()),
            'avg': float(df['CycleTime'].mean()),
            'total': float(df['CycleTime'].sum())
        }

        # 获取所有数据记录
        recent_data = []
        for _, row in df.iterrows():
            record = row.to_dict()
            # 安全地处理日期时间
            if pd.notnull(record['StartTestTime']):
                record['StartTestTime'] = record['StartTestTime'].strftime('%Y-%m-%d %H:%M:%S')
            else:
                record['StartTestTime'] = None
                
            if pd.notnull(record['EndTestTime']):
                record['EndTestTime'] = record['EndTestTime'].strftime('%Y-%m-%d %H:%M:%S')
            else:
                record['EndTestTime'] = None
                
            record['CycleTime'] = float(record['CycleTime']) if pd.notnull(record['CycleTime']) else 0
            
            # 计算S-Slot
            try:
                slave = int(record['Slave'])
                slot = int(record['Slot'])
                record['S_Slot'] = 16 * (slave - 1) + slot
            except (ValueError, TypeError):
                record['S_Slot'] = None
                
            recent_data.append(record)

        response_data = {
            'slave_distribution': slave_distribution,
            'slot_distribution': slot_distribution,
            'test_results': test_results,
            'ct_stats': ct_stats,
            'recent_data': recent_data
        }

        cursor.close()
        conn.close()

        return jsonify(response_data)

    except Exception as e:
        print(f"错误: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/slaves')
def get_slaves():
    try:
        station = request.args.get('station', 'AP5')
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)
        cursor.execute("SELECT DISTINCT Slave FROM log_entries WHERE Station = %s ORDER BY Slave", (station,))
        slaves = [row['Slave'] for row in cursor.fetchall()]
        
        cursor.close()
        conn.close()

        return jsonify(slaves)

    except Exception as e:
        print(f"错误: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/read_log', methods=['GET'])
def read_log():
    try:
        serial_number = request.args.get('serial_number')
        start_time = request.args.get('start_time')
        station = request.args.get('station')
        specified_log_path = request.args.get('log_path')

        if specified_log_path:
            if not os.path.exists(specified_log_path):
                return jsonify({'error': f'指定的日志文件不存在: {specified_log_path}'}), 404
            log_file_path = specified_log_path
        else:
            parsed_time = datetime.strptime(start_time, '%Y/%m/%d %H:%M:%S')
            formatted_date = parsed_time.strftime('%Y%m%d')
            time_pattern = parsed_time.strftime('%Y%m%d_%H%M')
            
            base_path = fr'\\10.70.9.22\Supplier\JOYNEXT_GP_Share\log\tmy\EOL_Log\{station}\{formatted_date}'

            if not os.path.exists(base_path):
                return jsonify({'error': f'日志目录不存在: {base_path}'}), 404

            matching_logs = []
            try:
                for root, dirs, files in os.walk(base_path):
                    for filename in fnmatch.filter(files, f'*{serial_number}*.log'):
                        full_path = os.path.join(root, filename)
                        matching_logs.append(full_path)
                        print(f"找到匹配文件: {full_path}")
            except Exception as e:
                print(f"搜索文件时出错: {str(e)}")
                return jsonify({'error': f'搜索日志文件时出错: {str(e)}'}), 500

            if not matching_logs:
                return jsonify({
                    'error': '未找到匹配的日志文件',
                    'debug_info': {
                        'base_path': base_path,
                        'time_pattern': time_pattern,
                        'serial_number': serial_number
                    }
                }), 404

            if len(matching_logs) > 1:
                return jsonify({
                    'multiple_logs': matching_logs,
                    'message': '找到多个匹配的日志文件'
                }), 300

            log_file_path = matching_logs[0]

        try:
            with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                log_content = f.readlines()
        except Exception as e:
            print(f"读取文件时出错: {str(e)}")
            return jsonify({'error': f'读取日志文件时出错: {str(e)}'}), 500

        # 用字典记录每个test_item的最后一次出现
        test_item_latest = {}
        for line in log_content:
            if "[Testing]" in line:
                match = re.search(
                    r"\[Testing\].*?Analyse\((.+?),\s*([\-\d.]+)(?:,\s*)?\):\s*(\w+)\s*\[LL:\s*([\-\d.]+),\s*UL:\s*([\-\d.]+)(?:,\s*ErrorCode:\s*(\d*))?\]",
                    line
                )
                if match:
                    test_item = match.group(1)
                    test_item_latest[test_item] = {
                        "test_item": test_item,
                        "test_value": match.group(2),
                        "result": match.group(3),
                        "lower_limit": match.group(4),
                        "upper_limit": match.group(5),
                        "error_code": match.group(6) or '',
                    }
        # 只保留每个测试项的最后一次记录
        parsed_results = list(test_item_latest.values())

        return jsonify({
            'parsed_results': parsed_results,
            'log_path': log_file_path
        })

    except ValueError as e:
        print(f"时间格式解析错误: {str(e)}")
        return jsonify({'error': f'时间格式解析错误: {str(e)}'}), 400
    except Exception as e:
        print(f"处理请求时出错: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE')
    return response

def parse_slave_slot_from_log(log_content):
    slave = None
    slot = None
    for line in log_content:
        # 同时匹配AP5和AP6的Slave格式
        if "Slave:" in line:
            match = re.search(r"AP[56] Slave:\s*(\d+)", line)
            if match:
                slave = match.group(1)
        elif "Slot of Slave" in line:
            match = re.search(r"Slot of Slave.*?:\s*(\d+)", line)
            if match:
                slot = match.group(1)
                
        if slave is not None and slot is not None:
            break
    return slave, slot

def parse_time_from_filename(filename):
    """从文件名解析时间，支持多种格式的日志文件名
    
    支持的格式示例:
    - AP6_20241121_234224_063A 25099295_00000000000014684526_PASS.log
    - AP5_20241121_234224_其他内容.log
    """
    try:
        # 查找符合格式的时间戳部分 (8位日期_6位时间)
        match = re.search(r'(\d{8})_(\d{6})', filename)
        if match:
            date_str = match.group(1)  # 20241121
            time_str = match.group(2)  # 234224
            
            # 格式化日期时间字符串
            full_time_str = f"{date_str[0:4]}/{date_str[4:6]}/{date_str[6:8]} {time_str[0:2]}:{time_str[2:4]}:{time_str[4:6]}"
            return datetime.strptime(full_time_str, '%Y/%m/%d %H:%M:%S')
    except Exception as e:
        print(f"解析文件名时间出错: {filename}, 错误: {str(e)}")
    return None

def parse_sn_from_filename(filename):
    """从文件名提取序列号，支持多种格式的日志文件名
    
    支持的格式示例:
    - AP6_20241121_234224_063A 25099295_00000000000014684526_PASS.log
    """
    try:
        # 尝试匹配20位数字的序列号
        match = re.search(r'_(\d{20})_', filename)
        if match:
            return match.group(1)
            
        # 如果没有找到20位序列号，尝试其他可能的序列号格式
        # 可以在这里添加其他序列号格式的匹配规则
        
    except Exception as e:
        print(f"解析文件名SN出错: {filename}, 错误: {str(e)}")
    return None

# 添加新的路由用于获取测试项趋势数据
@app.route('/api/item_trend')
def get_item_trend():
    time_range = request.args.get('time_range', '7')
    station = request.args.get('station', 'AP5')
    selected_slave = request.args.get('slave', '')
    item_name = request.args.get('item_name')
    custom_date = request.args.get('custom_date')

    def generate():
        try:
            print(f"\n=== 开始处理趋势数据请求 ===")
            print(f"测试项名称: {item_name}")
            print(f"站位: {station}")
            print(f"Slave: {selected_slave}")

            if not item_name:
                yield "data: " + json.dumps({
                    'type': 'error',
                    'error': '缺少测试项名称'
                }) + "\n\n"
                return

            start_date, end_date = get_date_filter(time_range, custom_date)
            
            # 收集文件列表
            total_files = 0
            file_list = []
            current_date = start_date
            while current_date <= end_date:
                formatted_date = current_date.strftime('%Y%m%d')
                base_path = fr'\\10.70.9.22\Supplier\JOYNEXT_GP_Share\log\tmy\EOL_Log\{station}\{formatted_date}'
                
                if os.path.exists(base_path):
                    for root, dirs, files in os.walk(base_path):
                        for filename in files:
                            if filename.endswith('.log'):
                                if not selected_slave or selected_slave in filename:
                                    file_list.append((os.path.join(root, filename), filename))
                                    total_files += 1
                current_date += timedelta(days=1)

            if total_files == 0:
                yield "data: " + json.dumps({
                    'type': 'error',
                    'error': '未找到任何日志文件'
                }) + "\n\n"
                return

            yield "data: " + json.dumps({
                'type': 'progress',
                'processed': 0,
                'total': total_files,
                'percentage': 0
            }) + "\n\n"

            values = []
            processed_files = 0
            last_progress_time = datetime.now()
            files_with_data = 0

            for (full_path, filename) in file_list:
                processed_files += 1
                try:
                    # 从文件名解析时间和SN
                    test_time = parse_time_from_filename(filename)
                    sn = parse_sn_from_filename(filename)
                    
                    if not test_time or not sn:
                        continue

                    with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                        log_content = f.readlines()
                        file_has_data = False
                        slave, slot = parse_slave_slot_from_log(log_content)

                        # 修改后的测试行匹配逻辑
                        for line in log_content:
                            if "[Testing]" in line:
                                # 使用更灵活的正则表达式模式，不依赖error code
                                match = re.search(
                                    rf"\[Testing\].*?Analyse\({re.escape(item_name)},\s*([-\d.]+)(?:,\s*)?\):\s*(\w+)\s*\[LL:\s*([-\d.]+),\s*UL:\s*([-\d.]+)(?:,\s*ErrorCode:\s*(\d*))?\]",
                                    line
                                )
                                
                                if match:
                                    try:
                                        test_value = float(match.group(1))
                                        test_result = match.group(2)
                                        lower_limit = float(match.group(3))
                                        upper_limit = float(match.group(4))
                                        error_code = match.group(5) or ''  # 如果ErrorCode为空，使用空字符串
                                        
                                        values.append({
                                            'sn': sn,
                                            'time': test_time.strftime('%Y-%m-%d %H:%M:%S'),
                                            'value': str(test_value),
                                            'result': test_result,
                                            'lower_limit': str(lower_limit),
                                            'upper_limit': str(upper_limit),
                                            'error_code': error_code,
                                            'slave': slave,
                                            'slot': slot
                                        })
                                        file_has_data = True
                                        
                                        if len(values) <= 5:
                                            print(f"找到测试值: sn={sn}, time={test_time}, value={test_value}, result={test_result}")
                                    except ValueError as ve:
                                        if processed_files <= 5:
                                            print(f"解析测试值出错: {str(ve)}, 行: {line.strip()}")
                                        continue

                        if file_has_data:
                            files_with_data += 1

                except Exception as e:
                    print(f"处理文件出错 {filename}: {str(e)}")
                    traceback.print_exc()
                    continue

                # 发送进度更新
                current_time = datetime.now()
                if processed_files % 10 == 0 or (current_time - last_progress_time).total_seconds() >= 1:
                    progress = round((processed_files / total_files) * 100, 1)
                    yield "data: " + json.dumps({
                        'type': 'progress',
                        'processed': processed_files,
                        'total': total_files,
                        'percentage': progress
                    }) + "\n\n"
                    last_progress_time = current_time

            print(f"\n=== 处理完成 ===")
            print(f"总文件数: {total_files}")
            print(f"包含数据的文件数: {files_with_data}")
            print(f"找到的有效值数量: {len(values)}")

            if values:
                values.sort(key=lambda x: x['time'])
                print(f"数据范围: {values[0]['time']} 到 {values[-1]['time']}")
                yield "data: " + json.dumps({
                    'type': 'complete',
                    'values': values
                }) + "\n\n"
            else:
                error_msg = {
                    'type': 'error',
                    'error': f'未找到任何有效测试值。已处理 {total_files} 个文件，'
                            f'包含数据的文件数: {files_with_data}。'
                            f'请检查测试项名称: {item_name} 是否正确。'
                }
                print(f"\n{error_msg}")
                yield "data: " + json.dumps(error_msg) + "\n\n"

        except Exception as e:
            print(f"处理过程发生错误: {str(e)}")
            traceback.print_exc()
            yield "data: " + json.dumps({
                'type': 'error',
                'error': f'处理数据时发生错误: {str(e)}'
            }) + "\n\n"

    return Response(
        stream_with_context(generate()), 
        mimetype='text/event-stream',
        headers={
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        }
    )

# 设备名称映射配置
DEVICE_NAME_MAPPING = {
    # 旧名称 -> 新名称
    'EOL1': 'SOC_1',
    'EOL2': 'SOC_2',
    'EOL3': 'SOC_3',
    'EOL5': 'SOC_4',
    'EOL4': 'VIU',
    'EOL#1': 'SOC_1',
    'EOL#2': 'SOC_2',
    'EOL#3': 'SOC_3',
    'EOL#5': 'SOC_4',
    'EOL#4': 'VIU'
}

# 反向映射：新名称 -> 旧名称列表
REVERSE_DEVICE_MAPPING = {
    'SOC_1': ['EOL1', 'EOL#1'],
    'SOC_2': ['EOL2', 'EOL#2'],
    'SOC_3': ['EOL3', 'EOL#3'],
    'SOC_4': ['EOL5', 'EOL#5'],
    'VIU': ['EOL4', 'EOL#4'],
    'VIU_1': ['VIU_1'],
    'VIU_2': ['VIU_2'],
    'VIU_3': ['VIU_3']
}

def get_display_device_name(original_name):
    """将数据库中的设备名称转换为显示名称"""
    return DEVICE_NAME_MAPPING.get(original_name, original_name)

def get_original_device_names(display_name):
    """根据显示名称获取对应的原始设备名称列表"""
    return REVERSE_DEVICE_MAPPING.get(display_name, [display_name])

def get_station_condition_sql(station):
    """根据站位类型生成SQL条件"""
    if station == 'SOC':
        # SOC包含：SOC_1, SOC_2, SOC_3, SOC_4 对应的原始名称
        all_names = []
        for display_name in ['SOC_1', 'SOC_2', 'SOC_3', 'SOC_4']:
            all_names.extend(get_original_device_names(display_name))
        names_str = "', '".join(all_names)
        return f"AND Station IN ('{names_str}')"
    elif station == 'VIU':
        # VIU包含：VIU, VIU_1, VIU_2, VIU_3 对应的原始名称
        all_names = []
        for display_name in ['VIU', 'VIU_1', 'VIU_2', 'VIU_3']:
            all_names.extend(get_original_device_names(display_name))
        names_str = "', '".join(all_names)
        return f"AND Station IN ('{names_str}')"
    else:
        return "AND Station = %s"

EOL_LOG_PATHS = {
    "SOC_1": r"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\8155EOL#1",
    "SOC_2": r"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\8155EOL#2",
    "SOC_3": r"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\8155EOL#3",
    "VIU": r"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\8155EOL#4",
    "SOC_4": r"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\8155EOL#5",
    # 保持向后兼容
    "EOL1": r"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\8155EOL#1",
    "EOL2": r"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\8155EOL#2",
    "EOL3": r"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\8155EOL#3",
    "EOL4": r"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\8155EOL#4",
    "EOL5": r"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\8155EOL#5",
}

@app.route('/api/lp8155/logs')
def get_lp8155_logs():
    eol = request.args.get('eol', 'SOC_1')  # SOC_1/SOC_2/SOC_3/SOC_4/VIU
    log_dir = EOL_LOG_PATHS.get(eol)
    if not log_dir or not os.path.exists(log_dir):
        return jsonify({'error': '路径不存在'}), 404
    logs = []
    for root, dirs, files in os.walk(log_dir):
        for file in files:
            if file.endswith('.csv'):
                logs.append({
                    'file': file,
                    'path': os.path.join(root, file)
                })
    return jsonify(logs)

@app.route('/api/lp8155/log_detail')
def get_lp8155_log_detail():
    serial_number = request.args.get('serial_number')
    test_time = request.args.get('test_time')
    station = request.args.get('station')

    if not serial_number or not test_time or not station:
        return jsonify({'error': '缺少必要参数'}), 400

    try:
        # 将前端传来的时间字符串转换为datetime对象
        test_time = datetime.strptime(test_time, '%Y/%m/%d %H:%M:%S')
        
        # 根据EOL站位确定基础路径
        station_map = {
            'SOC_1': '8155EOL#1',
            'SOC_2': '8155EOL#2',
            'SOC_3': '8155EOL#3',
            'VIU': '8155EOL#4',
            'SOC_4': '8155EOL#5',
            'VIU_1': '8155EOL#4',
            'VIU_2': '8155EOL#4',
            'VIU_3': '8155EOL#4',
            # 保持向后兼容
            'EOL1': '8155EOL#1',
            'EOL2': '8155EOL#2',
            'EOL3': '8155EOL#3',
            'EOL4': '8155EOL#4',
            'EOL5': '8155EOL#5'
        }

        if station not in station_map:
            return jsonify({'error': f'无效的站位: {station}'}), 400

        # 构建按日期分组的路径
        formatted_date = test_time.strftime('%Y%m%d')
        base_path = fr'\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\{station_map[station]}\{formatted_date}'
        print(f"查找路径: {base_path}")  # 调试日志

        if not os.path.exists(base_path):
            # 如果日期文件夹不存在，尝试在根目录查找（兼容旧文件结构）
            fallback_path = fr'\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\{station_map[station]}'
            print(f"日期文件夹不存在，尝试根目录: {fallback_path}")
            if os.path.exists(fallback_path):
                base_path = fallback_path
            else:
                return jsonify({'error': f'站位路径不存在: {base_path}'}), 404

        # 构建预期的文件名格式
        file_timestamp = test_time.strftime('%Y%m%d%H%M%S')

        # 根据设备类型确定文件名格式
        if station.startswith('VIU_'):
            # 新VIU设备格式：SN_通道号_时间.csv
            channel = station.split('_')[1]  # 提取通道号 (1, 2, 3)
            expected_filename = f"{serial_number}_{channel}_{file_timestamp}.csv"
            print(f"VIU设备预期文件名模式: {expected_filename}")  # 调试日志
        else:
            # 传统格式：SN_时间.csv
            expected_filename = f"{serial_number}_{file_timestamp}.csv"
            print(f"传统设备预期文件名模式: {expected_filename}")  # 调试日志

        # 在目录中查找文件
        matching_files = []
        for root, dirs, files in os.walk(base_path):
            print(f"搜索目录: {root}")  # 调试日志
            print(f"发现文件数: {len(files)}")  # 调试日志
            for file in files:
                if file.startswith(serial_number) and file.endswith('.csv'):
                    print(f"检查文件: {file}")  # 调试日志

                    # 根据设备类型使用不同的匹配模式
                    if station.startswith('VIU_'):
                        # VIU设备：SN_通道号_时间.csv
                        channel = station.split('_')[1]
                        pattern = rf'{re.escape(serial_number)}_{channel}_(\d{{14}})\.csv$'
                    else:
                        # 传统设备：SN_时间.csv
                        pattern = rf'{re.escape(serial_number)}_(\d{{14}})\.csv$'

                    file_time_match = re.search(pattern, file)
                    if file_time_match:
                        file_time_str = file_time_match.group(1)
                        try:
                            file_time = datetime.strptime(file_time_str, '%Y%m%d%H%M%S')
                            # 允许5分钟的时间误差
                            time_diff = abs((file_time - test_time).total_seconds())
                            if time_diff <= 300:  # 5分钟 = 300秒
                                matching_files.append(os.path.join(root, file))
                                print(f"找到匹配文件: {file}")  # 调试日志
                        except ValueError as ve:
                            print(f"时间解析错误: {file} - {str(ve)}")  # 调试日志
                            continue

        if not matching_files:
            print(f"未找到匹配文件。查找参数: serial={serial_number}, time={test_time}, station={station}")  # 调试日志
            return jsonify({'error': '未找到对应的日志文件'}), 404

        # 使用最匹配的文件
        log_file = matching_files[0]
        print(f"使用文件: {log_file}")  # 调试日志
        
        try:
            # 尝试不同的编码
            encodings = ['gb18030', 'gbk', 'gb2312', 'utf-8', 'latin1']
            log_data = None
            last_error = None
            
            for encoding in encodings:
                try:
                    print(f"尝试使用 {encoding} 编码读取文件")  # 调试日志
                    with open(log_file, 'r', encoding=encoding) as f:
                        lines = f.readlines()
                        
                        # 找到标题行的位置
                        header_row = None
                        for i, line in enumerate(lines):
                            if ('Test Seq' in line or 'Test Seg' in line) and 'step_name' in line:
                                header_row = i
                                break
                        
                        if header_row is not None:
                            # 使用pandas读取数据，从标题行开始
                            import io
                            data_text = ''.join(lines[header_row:])
                            df = pd.read_csv(io.StringIO(data_text))
                            
                            # 清理列名（去除空格）
                            df.columns = df.columns.str.strip()
                            
                            # 处理NaN值
                            df = df.replace({np.nan: None})
                            
                            # 转换为字典列表
                            log_data = []
                            for _, row in df.iterrows():
                                item = {}
                                for col in df.columns:
                                    value = row[col]
                                    # 特殊处理NaN和无限值
                                    if pd.isna(value) or (isinstance(value, float) and (np.isinf(value) or np.isnan(value))):
                                        item[col] = None
                                    else:
                                        item[col] = value
                                log_data.append(item)
                            
                            print(f"使用 {encoding} 编码成功")  # 调试日志
                            print(f"数据示例: {log_data[0] if log_data else 'No data'}")  # 调试日志
                            break
                            
                except Exception as e:
                    print(f"使用 {encoding} 编码失败: {str(e)}")  # 调试日志
                    last_error = e
                    continue
            
            if log_data is None:
                raise Exception(f"无法读取文件，所有编码尝试均失败。最后的错误: {str(last_error)}")

            return jsonify(log_data)
            
        except Exception as e:
            print(f"读取文件失败: {str(e)}")  # 调试日志
            return jsonify({'error': f'读取日志文件失败: {str(e)}'}), 500

    except Exception as e:
        print(f"处理请求失败: {str(e)}")  # 调试日志
        return jsonify({'error': f'处理请求失败: {str(e)}'}), 500

@app.route('/api/lp8155/db')
def get_lp8155_db():
    eol = request.args.get('eol', 'SOC_1')
    conn = get_db_connection()
    cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)
    cursor.execute("SELECT * FROM lp8155_eol_log WHERE Station=%s ORDER BY TestTime DESC LIMIT 100", (eol,))
    data = cursor.fetchall()

    # 转换设备名称显示
    for record in data:
        if 'Station' in record:
            record['Station'] = get_display_device_name(record['Station'])

    cursor.close()
    conn.close()
    return jsonify(data)

@app.route('/api/lp8155/top_issues')
def get_lp8155_top_issues():
    """获取Top Issue分布数据"""
    try:
        # 获取查询参数
        time_range = request.args.get('time_range', '0')  # 默认今天
        station = request.args.get('station', '')
        slave = request.args.get('slave', '')
        custom_date_start = request.args.get('custom_date_start', '')
        custom_date_end = request.args.get('custom_date_end', '')
        limit = int(request.args.get('limit', 10))

        # 构建时间条件
        time_condition = ""
        params = []

        if time_range == '0':  # 今天
            time_condition = "AND DATE(TestTime) = CURDATE()"
        elif time_range == '1':  # 昨天
            time_condition = "AND DATE(TestTime) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)"
        elif time_range == '7':  # 最近7天
            time_condition = "AND TestTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
        elif time_range == '30':  # 最近30天
            time_condition = "AND TestTime >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
        elif time_range == 'custom' and custom_date_start and custom_date_end:
            time_condition = "AND TestTime >= %s AND TestTime <= %s"
            params.extend([custom_date_start, custom_date_end])

        # 构建测试站位条件
        station_condition = ""
        if station:
            station_condition = get_station_condition_sql(station)
            if station not in ['SOC', 'VIU']:
                params.append(station)

        # 零跑项目数据库表中没有Slave字段，跳过Slave条件
        slave_condition = ""

        # 查询失败项统计
        conn = get_db_connection()
        cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)

        query = f"""
        SELECT
            SUBSTRING_INDEX(Failure_item, ',', 1) as failure_item,
            COUNT(*) as count
        FROM lp8155_eol_log
        WHERE TestResult = 'Failed'
        AND Failure_item IS NOT NULL
        AND Failure_item != ''
        AND Failure_item != '-'
        {time_condition}
        {station_condition}
        {slave_condition}
        GROUP BY SUBSTRING_INDEX(Failure_item, ',', 1)
        ORDER BY count DESC
        LIMIT %s
        """

        params.append(limit)
        cursor.execute(query, params)
        issues = cursor.fetchall()

        cursor.close()
        conn.close()

        return jsonify({
            'issues': issues,
            'total_issues': len(issues)
        })

    except Exception as e:
        print(f"获取Top Issue失败: {str(e)}")
        return jsonify({'error': f'获取Top Issue失败: {str(e)}'}), 500

@app.route('/api/lp8155/ai_analysis')
def get_ai_analysis():
    """获取AI分析结果"""
    try:
        # 获取查询参数
        time_range = request.args.get('time_range', '7')
        station = request.args.get('station', '')
        slave = request.args.get('slave', '')
        limit = int(request.args.get('limit', 10))

        # 创建AI分析器
        analyzer = AIAnalyzer(get_db_connection)

        # 执行AI分析
        result = analyzer.analyze(
            time_range=time_range,
            station=station,
            slave=slave,
            limit=limit
        )

        return jsonify(result)

    except Exception as e:
        print(f"AI分析失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/lp8155/ai_analysis_data')
def get_ai_analysis_data():
    """获取AI分析数据（用于客户端AI调用）"""
    try:
        # 获取查询参数
        time_range = request.args.get('time_range', '7')
        station = request.args.get('station', '')
        slave = request.args.get('slave', '')
        limit = int(request.args.get('limit', 10))

        # 创建AI分析器
        analyzer = AIAnalyzer(get_db_connection)

        # 只收集数据，不调用AI
        data = analyzer._collect_analysis_data(
            time_range=time_range,
            station=station,
            slave=slave,
            limit=limit
        )

        return jsonify({
            'success': True,
            'data': data
        })

    except Exception as e:
        print(f"获取AI分析数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/api/lp8155/failure_detail')
def get_failure_detail():
    """获取特定失败项的详细信息"""
    try:
        failure_item = request.args.get('failure_item', '')
        time_range = request.args.get('time_range', '0')
        station = request.args.get('station', '')
        slave = request.args.get('slave', '')
        custom_date_start = request.args.get('custom_date_start')
        custom_date_end = request.args.get('custom_date_end')

        if not failure_item:
            return jsonify({'error': '失败项参数不能为空'}), 400

        conn = get_db_connection()
        cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)

        # 构建时间条件
        now = datetime.now()
        params = []

        if custom_date_start and custom_date_end:
            start_date = datetime.strptime(custom_date_start, '%Y-%m-%dT%H:%M')
            end_date = datetime.strptime(custom_date_end, '%Y-%m-%dT%H:%M')
            time_condition = "AND TestTime >= %s AND TestTime <= %s"
            params.extend([start_date, end_date])
        elif time_range == '0':
            time_condition = "AND DATE(TestTime) = CURDATE()"
        elif time_range == '1':
            yesterday = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            today = yesterday + timedelta(days=1)
            time_condition = "AND TestTime >= %s AND TestTime < %s"
            params.extend([yesterday, today])
        else:
            days = int(time_range)
            start_date = (now - timedelta(days=days))
            time_condition = "AND TestTime >= %s AND TestTime <= %s"
            params.extend([start_date, now])

        # 构建站位条件
        station_condition = ""
        if station in ['SOC', 'VIU']:
            station_condition = get_station_condition_sql(station)

        # 零跑项目数据库表中没有Slave字段，跳过Slave条件
        slave_condition = ""

        query = f"""
        SELECT
            TestTime,
            Serial_Number,
            Station,
            Failure_item,
            COUNT(*) as occurrence_count
        FROM lp8155_eol_log
        WHERE TestResult = 'Failed'
        AND SUBSTRING_INDEX(Failure_item, ',', 1) = %s
        {time_condition}
        {station_condition}
        {slave_condition}
        GROUP BY TestTime, Serial_Number, Station, Failure_item
        ORDER BY TestTime DESC
        """

        params.insert(0, failure_item)  # 将failure_item参数插入到最前面

        print(f"失败项详情查询SQL: {query}")
        print(f"失败项详情查询参数: {params}")

        cursor.execute(query, params)
        details = cursor.fetchall()

        print(f"失败项详情查询结果数量: {len(details)}")
        if details:
            print(f"失败项详情查询结果示例: {details[0]}")

        # 统计设备分布
        station_stats = {}
        time_distribution = {}

        for detail in details:
            station = detail['Station']
            test_time = detail['TestTime']

            # 统计设备分布
            if station not in station_stats:
                station_stats[station] = 0
            station_stats[station] += detail['occurrence_count']

            # 统计时间分布（按小时）
            hour_key = test_time.strftime('%Y-%m-%d %H:00')
            if hour_key not in time_distribution:
                time_distribution[hour_key] = 0
            time_distribution[hour_key] += detail['occurrence_count']

        cursor.close()
        conn.close()

        return jsonify({
            'failure_item': failure_item,
            'total_occurrences': sum(station_stats.values()),
            'station_distribution': [
                {'station': k, 'count': v}
                for k, v in sorted(station_stats.items(), key=lambda x: x[1], reverse=True)
            ],
            'time_distribution': [
                {'time': k, 'count': v}
                for k, v in sorted(time_distribution.items())
            ],
            'details': details
        })

    except Exception as e:
        print(f"获取失败项详情失败: {str(e)}")
        return jsonify({'error': f'获取失败项详情失败: {str(e)}'}), 500

@app.route('/api/lp8155/item_trend')
def get_lp8155_item_trend():
    """获取零跑项目测试项趋势数据"""
    try:
        # 获取查询参数
        item_name = request.args.get('item_name', '')
        time_range = request.args.get('time_range', '7')
        station = request.args.get('station', '')
        eol = request.args.get('eol', '')
        custom_date_start = request.args.get('custom_date_start')
        custom_date_end = request.args.get('custom_date_end')

        print(f"零跑趋势API调用: item_name={item_name}, time_range={time_range}, station={station}, eol={eol}")

        if not item_name:
            return jsonify({'error': '测试项名称不能为空'}), 400

        def generate():
            try:
                yield f'data: {{"type": "progress", "percentage": 10, "processed": 0, "total": 0}}\n\n'

                conn = get_db_connection()
                cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)

                # 构建时间条件
                now = datetime.now()
                params = []

                if custom_date_start and custom_date_end:
                    start_date = datetime.strptime(custom_date_start, '%Y-%m-%dT%H:%M')
                    end_date = datetime.strptime(custom_date_end, '%Y-%m-%dT%H:%M')
                    time_condition = "AND TestTime >= %s AND TestTime <= %s"
                    params.extend([start_date, end_date])
                elif time_range == '0':
                    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    time_condition = "AND TestTime >= %s AND TestTime <= %s"
                    params.extend([today_start, now])
                elif time_range == '1':
                    yesterday = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
                    today = yesterday + timedelta(days=1)
                    time_condition = "AND TestTime >= %s AND TestTime < %s"
                    params.extend([yesterday, today])
                else:
                    days = int(time_range)
                    start_date = (now - timedelta(days=days))
                    time_condition = "AND TestTime >= %s AND TestTime <= %s"
                    params.extend([start_date, now])

                # 构建站位条件（基于主页选择的station）
                station_condition = ""
                if station in ['SOC', 'VIU']:
                    station_condition = get_station_condition_sql(station)

                # 构建EOL条件
                eol_condition = ""
                if eol:
                    eol_condition = f"AND Station LIKE '%{eol}%'"

                yield f'data: {{"type": "progress", "percentage": 30, "processed": 0, "total": 0}}\n\n'

                # 查询包含该测试项的所有测试记录，不限制数量
                query = f"""
                SELECT DISTINCT TestTime, Serial_Number, Station, TestResult
                FROM lp8155_eol_log
                WHERE 1=1
                {time_condition}
                {station_condition}
                {eol_condition}
                ORDER BY TestTime DESC
                """

                cursor.execute(query, params)
                test_records = cursor.fetchall()

                yield f'data: {{"type": "progress", "percentage": 50, "processed": 0, "total": {len(test_records)}}}\n\n'

                trend_data = []
                processed_count = 0
                limit_warnings = []  # 存储上下限不一致的警告
                seen_limits = set()  # 用于跟踪已见过的上下限组合

                # 遍历每个测试记录，查找对应的CSV文件
                for record in test_records:
                    try:
                        processed_count += 1

                        # 每处理5个记录发送一次进度更新
                        if processed_count % 5 == 0 or processed_count == len(test_records):
                            progress = 50 + int((processed_count / len(test_records)) * 40)
                            yield f'data: {{"type": "progress", "percentage": {progress}, "processed": {processed_count}, "total": {len(test_records)}}}\n\n'

                        # 根据站位确定文件路径
                        station_name = record['Station']
                        test_time = record['TestTime']
                        serial_number = record['Serial_Number']

                        # 构建按日期分组的路径
                        formatted_date = test_time.strftime('%Y%m%d')

                        # 使用映射函数获取文件路径
                        path_mapping = {
                            'EOL1': '8155EOL#1', 'EOL#1': '8155EOL#1', 'SOC_1': '8155EOL#1',
                            'EOL2': '8155EOL#2', 'EOL#2': '8155EOL#2', 'SOC_2': '8155EOL#2',
                            'EOL3': '8155EOL#3', 'EOL#3': '8155EOL#3', 'SOC_3': '8155EOL#3',
                            'EOL4': '8155EOL#4', 'EOL#4': '8155EOL#4', 'VIU': '8155EOL#4',
                            'EOL5': '8155EOL#5', 'EOL#5': '8155EOL#5', 'SOC_4': '8155EOL#5',
                            'VIU_1': '8155EOL#4', 'VIU_2': '8155EOL#4', 'VIU_3': '8155EOL#4'
                        }

                        folder_name = path_mapping.get(station_name)
                        if folder_name:
                            base_path = fr"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\{folder_name}\{formatted_date}"
                            fallback_path = fr"\\10.70.9.23\Plant_Station_File_Share\LEAP_8155\{folder_name}"
                        else:
                            continue

                        # 如果日期文件夹不存在，尝试根目录
                        if not os.path.exists(base_path):
                            base_path = fallback_path

                        # 构建文件名
                        time_str = test_time.strftime('%Y%m%d%H%M%S')
                        filename = f"{serial_number}_{time_str}.csv"
                        file_path = os.path.join(base_path, filename)

                        # 读取CSV文件并查找测试项，只读取前500行提高性能
                        if os.path.exists(file_path):
                            # 先读取文件找到标题行
                            with open(file_path, 'r', encoding='gb18030') as f:
                                lines = f.readlines()

                                # 找到标题行的位置
                                header_row = None
                                for i, line in enumerate(lines):
                                    if ('Test Seq' in line or 'Test Seg' in line) and 'step_name' in line:
                                        header_row = i
                                        break

                                if header_row is None:
                                    continue  # 如果没有找到标题行，跳过这个文件

                                # 使用pandas读取数据，从标题行开始，读取所有数据
                                import io
                                data_text = ''.join(lines[header_row:])  # 读取从标题行开始的所有数据
                                df = pd.read_csv(io.StringIO(data_text))

                            # 查找匹配的测试项
                            for _, row in df.iterrows():
                                test_seq = str(row.get('Test Seq', ''))
                                step_name = str(row.get('step_name', ''))
                                full_item_name = f"{test_seq}-{step_name}"

                                if full_item_name == item_name:
                                    measured_value = row.get('MeasuredValue')
                                    low_limit = row.get('LowLimit')
                                    high_limit = row.get('HighLimit')

                                    if pd.notna(measured_value) and measured_value != '':
                                        try:
                                            value = float(measured_value)
                                            result = str(row.get('Result', '')).strip()

                                            # 检查上下限是否一致
                                            if pd.notna(low_limit) and pd.notna(high_limit):
                                                current_limits = (float(low_limit), float(high_limit))
                                                if seen_limits and current_limits not in seen_limits:
                                                    # 发现不一致的上下限
                                                    existing_limits = list(seen_limits)[0] if seen_limits else None
                                                    warning_msg = f"测试项 {item_name} 上下限不一致: {existing_limits} vs {current_limits}"
                                                    limit_warnings.append({
                                                        'type': 'limit_warning',
                                                        'message': warning_msg,
                                                        'serial_number': serial_number,
                                                        'test_time': test_time.isoformat()
                                                    })
                                                seen_limits.add(current_limits)

                                            trend_data.append({
                                                'test_time': test_time.isoformat(),
                                                'test_value': value,
                                                'station': station_name,
                                                'eol': station_name,
                                                'serial_number': serial_number,
                                                'result': result,
                                                'low_limit': float(low_limit) if pd.notna(low_limit) else None,
                                                'high_limit': float(high_limit) if pd.notna(high_limit) else None
                                            })
                                            break
                                        except (ValueError, TypeError):
                                            continue
                    except Exception as e:
                        print(f"处理文件失败: {e}")
                        continue

                print(f"数据处理完成，共找到 {len(trend_data)} 条趋势数据，{len(limit_warnings)} 个警告")

                yield f'data: {{"type": "progress", "percentage": 95, "processed": {len(test_records)}, "total": {len(test_records)}}}\n\n'

                cursor.close()
                conn.close()

                # 发送完成消息，包含警告信息
                result_data = {
                    'type': 'complete',
                    'values': trend_data,
                    'warnings': limit_warnings
                }

                print(f"准备发送完成消息，数据长度: {len(trend_data)}")
                yield f'data: {json.dumps(result_data, ensure_ascii=False)}\n\n'
                print("完成消息已发送")

            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                print(f"获取测试项趋势数据失败: {str(e)}")
                print(f"详细错误信息: {error_details}")
                yield f'data: {{"type": "error", "error": "获取数据失败: {str(e)}"}}\n\n'

        return Response(generate(), mimetype='text/event-stream')

    except Exception as e:
        print(f"获取测试项趋势数据失败: {str(e)}")
        return jsonify({'error': f'获取数据失败: {str(e)}'}), 500

@app.route('/api/leap8155/data')
def get_leap8155_data():
    eol = request.args.get('eol', '')  # 改为可选参数
    test_station = request.args.get('test_station', '')  # 新增测试站位参数
    time_range = request.args.get('time_range', '7')
    custom_date_start = request.args.get('custom_date_start')
    custom_date_end = request.args.get('custom_date_end')
    result = request.args.get('result', '')

    conn = get_db_connection()
    cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)

    now = datetime.now()
    if custom_date_start and custom_date_end:
        # 使用精确的时间范围
        start_date = datetime.strptime(custom_date_start, '%Y-%m-%dT%H:%M')
        end_date = datetime.strptime(custom_date_end, '%Y-%m-%dT%H:%M')
        sql = "SELECT * FROM lp8155_eol_log WHERE TestTime >= %s AND TestTime <= %s"
        params = [start_date, end_date]
    elif time_range == '0':
        # 今天
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        sql = "SELECT * FROM lp8155_eol_log WHERE TestTime >= %s AND TestTime <= %s"
        params = [today_start, now]
    elif time_range == '1':
        # 昨天
        yesterday = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        today = yesterday + timedelta(days=1)
        sql = "SELECT * FROM lp8155_eol_log WHERE TestTime >= %s AND TestTime < %s"
        params = [yesterday, today]
    else:
        # 最近7天或30天
        days = int(time_range)
        start_date = (now - timedelta(days=days))
        sql = "SELECT * FROM lp8155_eol_log WHERE TestTime >= %s AND TestTime <= %s"
        params = [start_date, now]

    # 添加测试站位筛选条件
    # 支持两种Station名称格式：带#号和不带#号
    if test_station in ['SOC', 'VIU']:
        sql += " " + get_station_condition_sql(test_station)

    # 添加EOL设备筛选条件
    if eol:
        sql += " AND Station=%s"
        params.append(eol)

    if result:
        if result == 'Other':
            # Other选项：筛选出非Pass和Fail的所有结果
            sql += " AND TestResult NOT IN ('PASSED', 'FAILED', 'Pass', 'Fail', 'Passed', 'Failed')"
        else:
            sql += " AND TestResult=%s"
            params.append(result)

    sql += " ORDER BY TestTime DESC"
    cursor.execute(sql, params)
    data = cursor.fetchall()

    # 转换设备名称显示
    for record in data:
        if 'Station' in record:
            record['Station'] = get_display_device_name(record['Station'])

    cursor.close()
    conn.close()

    return jsonify(data)

@app.route('/api/search_by_sn')
def search_by_sn():
    try:
        sn_type = request.args.get('sn_type', 'Serial_Number')
        sn_value = request.args.get('sn_value', '')
        
        if not sn_value:
            return jsonify({'error': '序列号不能为空'}), 400

        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)
        
        # 构建查询语句
        sql = """
            SELECT 
                StartTestTime,
                EndTestTime,
                Serial_Number,
                Customer_Serial_Number,
                Slave,
                Slot,
                TestResult,
                Station,
                Type,
                CycleTime
            FROM log_entries 
            WHERE {} LIKE %s
            ORDER BY StartTestTime DESC
        """.format(sn_type)
        
        # 使用模糊匹配
        cursor.execute(sql, [f'%{sn_value}%'])
        results = cursor.fetchall()
        
        # 将结果转换为DataFrame以便复用现有的数据处理逻辑
        df = pd.DataFrame(results)
        
        if len(df) == 0:
            return jsonify({
                'slave_distribution': [],
                'slot_distribution': [],
                'test_results': {
                    'total': 0,
                    'pass': 0,
                    'fail': 0
                },
                'ct_stats': {
                    'min': 0,
                    'max': 0,
                    'avg': 0,
                    'total': 0
                },
                'recent_data': []
            })

        # 处理日期列
        df['StartTestTime'] = pd.to_datetime(df['StartTestTime'])
        df['EndTestTime'] = pd.to_datetime(df['EndTestTime'])
        df = df.replace({pd.NaT: None})

        # Slave分布计算
        slave_stats = df.groupby(['Slave', 'TestResult']).size().unstack(fill_value=0)
        slave_distribution = []
        for slave in slave_stats.index:
            total = slave_stats.loc[slave].sum()
            passed = slave_stats.loc[slave, 'PASSED'] if 'PASSED' in slave_stats.columns else 0
            failed = slave_stats.loc[slave, 'FAILED'] if 'FAILED' in slave_stats.columns else 0
            fail_rate = (failed / total * 100) if total > 0 else 0
            slave_distribution.append({
                'name': slave,
                'passed': int(passed),
                'failed': int(failed),
                'failRate': round(fail_rate, 2)
            })

        # Slot分布计算
        slot_stats = df.groupby(['Slot', 'TestResult']).size().unstack(fill_value=0)
        slot_distribution = []
        for slot in slot_stats.index:
            total = slot_stats.loc[slot].sum()
            passed = slot_stats.loc[slot, 'PASSED'] if 'PASSED' in slot_stats.columns else 0
            failed = slot_stats.loc[slot, 'FAILED'] if 'FAILED' in slot_stats.columns else 0
            fail_rate = (failed / total * 100) if total > 0 else 0
            slot_distribution.append({
                'name': slot,
                'passed': int(passed),
                'failed': int(failed),
                'failRate': round(fail_rate, 2)
            })

        # 测试结果统计
        test_results = {
            'total': len(df),
            'pass': len(df[df['TestResult'] == 'PASSED']),
            'fail': len(df[df['TestResult'] == 'FAILED'])
        }

        # CT统计
        ct_stats = {
            'min': float(df['CycleTime'].min()),
            'max': float(df['CycleTime'].max()),
            'avg': float(df['CycleTime'].mean()),
            'total': float(df['CycleTime'].sum())
        }

        # 获取所有数据记录
        recent_data = []
        for _, row in df.iterrows():
            record = row.to_dict()
            if pd.notnull(record['StartTestTime']):
                record['StartTestTime'] = record['StartTestTime'].strftime('%Y-%m-%d %H:%M:%S')
            else:
                record['StartTestTime'] = None
                
            if pd.notnull(record['EndTestTime']):
                record['EndTestTime'] = record['EndTestTime'].strftime('%Y-%m-%d %H:%M:%S')
            else:
                record['EndTestTime'] = None
                
            record['CycleTime'] = float(record['CycleTime']) if pd.notnull(record['CycleTime']) else 0
            
            try:
                slave = int(record['Slave'])
                slot = int(record['Slot'])
                record['S_Slot'] = 16 * (slave - 1) + slot
            except (ValueError, TypeError):
                record['S_Slot'] = None
                
            recent_data.append(record)

        response_data = {
            'slave_distribution': slave_distribution,
            'slot_distribution': slot_distribution,
            'test_results': test_results,
            'ct_stats': ct_stats,
            'recent_data': recent_data
        }

        cursor.close()
        conn.close()

        return jsonify(response_data)

    except Exception as e:
        print(f"错误: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/leap8155/search_by_sn')
def search_leap_by_sn():
    try:
        sn_type = request.args.get('sn_type', 'Serial_Number')
        sn_value = request.args.get('sn_value', '')
        
        if not sn_value:
            return jsonify({'error': '序列号不能为空'}), 400

        conn = get_db_connection()
        if not conn:
            return jsonify({'error': '数据库连接失败'}), 500

        cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)
        
        # 使用精确匹配
        sql = """
            SELECT * FROM lp8155_eol_log 
            WHERE {} = %s
            ORDER BY TestTime DESC
        """.format(sn_type)
        
        cursor.execute(sql, [sn_value])
        results = cursor.fetchall()
        
        cursor.close()
        conn.close()

        return jsonify(results)

    except Exception as e:
        print(f"错误: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/log_ai_analysis', methods=['POST'])
def log_ai_analysis():
    """记录AI分析操作日志"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        # 获取客户端IP地址
        if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
            ip = request.environ['REMOTE_ADDR']
        else:
            ip = request.environ['HTTP_X_FORWARDED_FOR']

        # 获取User-Agent
        user_agent = request.headers.get('User-Agent', 'Unknown')

        # 构建AI分析日志条目
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        action = data.get('action', 'unknown')

        # 根据不同的动作构建不同的日志内容
        if action == 'ai_analysis_start':
            params = data.get('parameters', {})
            path_info = f"AI分析开始 - 时间范围:{params.get('time_range', 'N/A')}, 站位:{params.get('station', 'N/A')}, 限制:{params.get('limit', 'N/A')}"
        elif action == 'ai_analysis_cancelled':
            path_info = "AI分析取消 - 用户取消了收费确认"
        elif action == 'ai_analysis_completed':
            summary = data.get('analysis_summary', '分析完成')
            data_source = data.get('data_source', '未知')
            path_info = f"AI分析完成 - 数据源:{data_source}, 摘要:{summary}"
        elif action == 'ai_analysis_failed':
            error = data.get('error', '未知错误')
            path_info = f"AI分析失败 - 错误:{error}"
        else:
            path_info = f"AI分析操作 - {action}"

        # 记录到访问日志
        log_access(ip, user_agent, path_info, 'AI_ANALYSIS')

        return jsonify({'success': True, 'message': 'AI分析日志记录成功'})

    except Exception as e:
        print(f"记录AI分析日志失败: {e}")
        return jsonify({'error': f'记录AI分析日志失败: {str(e)}'}), 500

@app.route('/api/access_log')
def get_access_log():
    """读取、解析并返回访问日志"""
    try:
        log_file_path = get_access_log_path()

        if not os.path.exists(log_file_path):
            return jsonify([])

        log_entries = []
        # 更健壮的正则表达式，处理可能不完整的行
        log_pattern = re.compile(
            r'\[(?P<timestamp>.*?)\]\s+'
            r'IP: (?P<ip>.*?)\s*\|'
            r'\s*Method: (?P<method>.*?)\s*\|'
            r'\s*Path: (?P<path>.*?)\s*\|'
            r'\s*User-Agent: (?P<user_agent>.*)'
        )

        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            lines.reverse()  # 最新记录在最前面
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue

                match = log_pattern.match(line)
                if match:
                    log_entries.append(match.groupdict())
                else:
                    # 对于格式不正确的行，尝试简单处理或跳过
                    print(f"跳过格式不正确的日志行 {line_num}: {line[:100]}...")

        # 如果没有解析到任何日志条目，返回原始内容
        if not log_entries:
            with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                raw_content = f.read()
                return jsonify({'raw_content': raw_content})

        return jsonify(log_entries)

    except Exception as e:
        print(f"读取访问日志失败: {e}")
        traceback.print_exc()
        return jsonify({'error': f'读取访问日志失败: {str(e)}'}), 500

# 版本管理功能
VERSION_FILE = 'version.json'

# 全局变量存储服务启动时间
SERVICE_START_TIME = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def load_version_info():
    """加载版本信息"""
    print(f"[版本信息] 开始加载版本信息，服务启动时间: {SERVICE_START_TIME}")
    print(f"[版本信息] 当前工作目录: {os.getcwd()}")

    # 获取exe文件所在目录
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        exe_dir = os.path.dirname(sys.executable)
        print(f"[版本信息] 检测到exe环境，exe目录: {exe_dir}")
    else:
        # 如果是Python脚本
        exe_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"[版本信息] 检测到开发环境，脚本目录: {exe_dir}")

    # 首先尝试直接读取version.json（更可靠的方式）
    version_info = None

    # 尝试多个可能的version.json位置，优先使用exe目录
    possible_paths = [
        os.path.join(exe_dir, 'version.json'),  # exe目录下的version.json
        os.path.join(exe_dir, '..', 'version.json'),  # exe父目录下的version.json（如果exe在dist目录中）
        'version.json',  # 当前工作目录下的version.json
        './version.json',
        'dist/version.json',
        './dist/version.json'
    ]

    for version_file in possible_paths:
        try:
            abs_path = os.path.abspath(version_file)
            print(f"[版本信息] 检查文件: {version_file} -> {abs_path}")
            if os.path.exists(version_file):
                print(f"[版本信息] 找到version.json文件: {version_file}")
                print(f"[版本信息] 绝对路径: {abs_path}")
                print(f"[版本信息] 文件大小: {os.path.getsize(version_file)} bytes")
                print(f"[版本信息] 修改时间: {datetime.fromtimestamp(os.path.getmtime(version_file))}")

                with open(version_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"[版本信息] 原始文件内容: {repr(content)}")

                    # 重新解析JSON
                    data = json.loads(content)
                    print(f"[版本信息] 解析后的数据: {data}")

                    # 验证关键字段
                    if 'version' not in data:
                        print(f"[版本信息] 警告: 文件缺少version字段")
                        continue

                    # 获取版本号，确保只有一个v前缀
                    raw_version = data.get('version', '1.0.0')
                    if raw_version.startswith('v'):
                        version_display = raw_version
                    else:
                        version_display = f"v{raw_version}"

                    version_info = {
                        'version': version_display,
                        'build_time': data.get('build_time', '开发版本'),
                        'service_start_time': SERVICE_START_TIME,
                        'release_date': data.get('build_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                        'build_count': data.get('build_count', 1),
                        'display_text': version_display
                    }
                    print(f"[版本信息] 构建的版本信息: {version_info}")
                    return version_info
            else:
                print(f"[版本信息] 文件不存在: {version_file}")
        except Exception as e:
            print(f"[版本信息] 读取{version_file}失败: {e}")
            import traceback
            print(f"[版本信息] 详细错误: {traceback.format_exc()}")
            continue

    # 如果直接读取失败，尝试version_manager
    try:
        print("[版本信息] 尝试从version_manager加载...")
        from version_manager import get_version_info
        version_info = get_version_info()
        print(f"[版本信息] version_manager加载成功: {version_info}")

        # 添加服务启动时间
        version_info['service_start_time'] = SERVICE_START_TIME

        # 确保有 release_date 字段
        if 'release_date' not in version_info and 'build_time' in version_info:
            try:
                build_datetime = datetime.strptime(version_info['build_time'], '%Y-%m-%d %H:%M:%S')
                version_info['release_date'] = build_datetime.strftime('%Y-%m-%d %H:%M:%S')
            except:
                version_info['release_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        print(f"[版本信息] 最终版本信息: {version_info}")
        return version_info
    except Exception as e:
        print(f"[版本信息] version_manager加载失败: {str(e)}")
    # 如果所有方法都失败，使用默认版本信息
    print("[版本信息] 所有加载方法都失败，使用默认版本信息")
    default_info = {
        'version': 'v1.0.0',
        'build_time': '开发版本',
        'service_start_time': SERVICE_START_TIME,
        'release_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'build_count': 1,
        'display_text': 'v1.0.0'
    }
    print(f"[版本信息] 默认版本信息: {default_info}")
    return default_info

def save_version_info(version_info):
    """保存版本信息"""
    try:
        with open(VERSION_FILE, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存版本信息失败: {str(e)}")

def increment_version():
    """版本自动递增（仅用于打包时）"""
    try:
        from version_manager import increment_version as inc_version
        new_version, build_time, build_count = inc_version()

        # 返回格式化的版本信息
        return {
            'version': f"v{new_version}",
            'build_time': build_time,
            'service_start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'build_count': build_count,
            'display_text': f"v{new_version}"
        }
    except Exception as e:
        print(f"版本递增失败: {str(e)}")
        return load_version_info()

@app.route('/api/version')
def get_version():
    """获取当前版本信息"""
    try:
        version_info = load_version_info()
        return jsonify(version_info)
    except Exception as e:
        print(f"获取版本信息失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/version/increment', methods=['POST'])
def increment_version_api():
    """手动递增版本号"""
    try:
        version_info = increment_version()
        return jsonify({
            'success': True,
            'message': f'版本已更新为 {version_info["version"]}',
            'version_info': version_info
        })
    except Exception as e:
        print(f"版本递增失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

# 在应用启动时初始化版本信息
def init_version_info():
    """初始化版本信息（不再自动递增版本）"""
    try:
        # 无论是exe还是开发模式，都只加载版本信息，不自动递增
        # 版本递增只在手动打包时进行
        load_version_info()
        print("版本信息已初始化")
    except Exception as e:
        print(f"版本初始化失败: {str(e)}")

@app.route('/api/ai_config', methods=['GET', 'POST'])
def ai_config_api():
    """AI配置管理API"""
    from ai_config import AIConfig, AI_PROVIDERS

    # 创建配置实例
    ai_config = AIConfig()

    if request.method == 'GET':
        # 获取当前配置
        return jsonify({
            'success': True,
            'config': {
                'provider': ai_config.get('provider'),
                'model': ai_config.get('model'),
                'max_tokens': ai_config.get('max_tokens'),
                'temperature': ai_config.get('temperature'),
                'cache_enabled': ai_config.get('cache_enabled'),
                'cache_duration_hours': ai_config.get('cache_duration_hours'),
                'client_side_ai': ai_config.get('client_side_ai'),
                'is_configured': ai_config.is_configured()
            },
            'providers': AI_PROVIDERS
        })

    elif request.method == 'POST':
        # 更新配置
        try:
            data = request.get_json()

            # 更新配置项
            for key in ['provider', 'model', 'api_key', 'max_tokens', 'temperature',
                       'cache_enabled', 'cache_duration_hours']:
                if key in data:
                    ai_config.set(key, data[key])

            return jsonify({
                'success': True,
                'message': 'AI配置已更新',
                'is_configured': ai_config.is_configured()
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

@app.route('/api/deepseek/proxy', methods=['POST'])
def deepseek_proxy():
    """DeepSeek API代理，解决CORS问题"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        # 直接使用固定的API密钥
        api_key = 'sk-b28e0b5d4412410db203c87809ccb9ad'

        print(f"🌐 DeepSeek代理请求，API密钥: {api_key[:10]}...")

        # 添加SSL验证禁用
        try:
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        except ImportError:
            pass

        # 构建请求头
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

        # 发送请求到DeepSeek API
        response = requests.post(
            'https://api.deepseek.com/v1/chat/completions',
            headers=headers,
            json=data,
            timeout=60,
            verify=False
        )

        print(f"📥 DeepSeek API响应状态: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ DeepSeek API调用成功")
            return jsonify(result)
        else:
            error_text = response.text
            print(f"❌ DeepSeek API调用失败: {response.status_code} - {error_text}")
            return jsonify({
                'error': f'DeepSeek API调用失败: {response.status_code}',
                'details': error_text
            }), response.status_code

    except Exception as e:
        print(f"❌ DeepSeek代理异常: {e}")
        traceback.print_exc()
        return jsonify({'error': f'代理请求失败: {str(e)}'}), 500

@app.route('/api/deepseek/balance', methods=['GET'])
def get_deepseek_balance():
    """获取DeepSeek API余额"""
    try:
        print("🔍 开始查询DeepSeek余额...")

        # 直接使用固定的API密钥，避免导入问题
        api_key = 'sk-b28e0b5d4412410db203c87809ccb9ad'

        if not api_key:
            print("❌ API密钥未配置")
            return jsonify({
                'success': False,
                'error': 'API密钥未配置'
            }), 400

        headers = {
            'Accept': 'application/json',
            'Authorization': f'Bearer {api_key}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        print(f"📡 发送余额查询请求...")

        # 添加SSL验证禁用和更长的超时时间，适配exe环境
        try:
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        except ImportError:
            pass  # urllib3不可用时忽略警告禁用

        response = requests.get(
            'https://api.deepseek.com/user/balance',
            headers=headers,
            timeout=60,  # 增加超时时间
            verify=False,  # 禁用SSL验证，解决exe打包后的证书问题
            allow_redirects=True
        )

        print(f"📊 余额查询响应状态: {response.status_code}")

        if response.status_code == 200:
            balance_data = response.json()
            print(f"✅ 余额查询成功: {balance_data}")
            return jsonify({
                'success': True,
                'balance': balance_data
            })
        else:
            error_msg = f'获取余额失败: HTTP {response.status_code}'
            try:
                error_detail = response.json()
                error_msg += f' - {error_detail}'
            except:
                error_msg += f' - {response.text[:200]}'

            print(f"❌ {error_msg}")
            return jsonify({
                'success': False,
                'error': error_msg
            }), response.status_code

    except requests.exceptions.Timeout:
        error_msg = '余额查询超时，请检查网络连接'
        print(f"⏰ {error_msg}")
        return jsonify({
            'success': False,
            'error': error_msg
        }), 408
    except requests.exceptions.ConnectionError:
        error_msg = '无法连接到DeepSeek API，请检查网络连接'
        print(f"🌐 {error_msg}")
        return jsonify({
            'success': False,
            'error': error_msg
        }), 503
    except Exception as e:
        error_msg = f'余额查询异常: {str(e)}'
        print(f"❌ {error_msg}")
        return jsonify({
            'success': False,
            'error': error_msg
        }), 500

# 应用启动时执行版本初始化
init_version_info()

if __name__ == '__main__':
    # Python 3.13兼容性修复：禁用调试模式避免watchdog问题
    import sys
    if sys.version_info >= (3, 13):
        print("⚠️  检测到Python 3.13，禁用调试模式以避免兼容性问题")
        app.run(debug=False, host='0.0.0.0', port=5000)
    else:
        app.run(debug=True, host='0.0.0.0', port=5000)

# 构建说明:
# 1. 使用 python build_final.py 自动递增版本并构建exe
#    - pyinstaller --onefile --add-data "static;static" --name TestDataAnalysisSystem app.py
#      python -m PyInstaller --onefile --add-data "static;static" --name TestDataAnalysisSystem app.py