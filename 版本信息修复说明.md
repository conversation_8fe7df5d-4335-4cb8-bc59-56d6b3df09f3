# 版本信息修复说明

## 问题描述

1. **本地问题**：打包好的exe在本地运行时，"服务启动"时间显示的是发布日期，而不是exe启动时间
2. **服务器问题**：同样的exe移动到服务器后，版本信息显示异常（如显示"vv1.0.0"、"开发版本"等）

## 修复方案

### 1. 服务启动时间修复

**问题原因**：之前使用`build_time`作为服务启动时间，这是构建时间而不是运行时间。

**修复方法**：
- 在`app.py`中添加全局变量`SERVICE_START_TIME`，在模块加载时记录真实的服务启动时间
- 修改`load_version_info()`函数，确保`service_start_time`字段使用exe启动时的时间
- 修改前端代码，使用`service_start_time`而不是`build_time`

### 2. 服务器环境兼容性修复

**问题原因**：服务器环境下可能缺少某些模块或文件路径不同，导致版本信息加载失败。

**修复方法**：
- 重构`load_version_info()`函数，优先直接读取`version.json`文件
- 添加多个可能的文件路径搜索
- 改进异常处理，确保在任何环境下都能正确显示版本信息
- 修复版本号重复添加"v"前缀的问题

## 修改的文件

### app.py
```python
# 全局变量存储服务启动时间
SERVICE_START_TIME = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def load_version_info():
    """加载版本信息"""
    # 优先直接读取version.json文件
    possible_paths = ['version.json', './version.json', 'dist/version.json', './dist/version.json']
    
    for version_file in possible_paths:
        try:
            if os.path.exists(version_file):
                with open(version_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    # 处理版本号，确保只有一个v前缀
                    raw_version = data.get('version', '1.0.0')
                    if raw_version.startswith('v'):
                        version_display = raw_version
                    else:
                        version_display = f"v{raw_version}"
                    
                    return {
                        'version': version_display,
                        'build_time': data.get('build_time', '开发版本'),
                        'service_start_time': SERVICE_START_TIME,  # 使用真实的启动时间
                        'release_date': data.get('build_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                        'build_count': data.get('build_count', 1),
                        'display_text': version_display
                    }
        except Exception as e:
            continue
    
    # 备用方案：尝试version_manager模块
    # 最终备用方案：返回默认信息
```

### static/index.html
```javascript
function loadVersionInfo() {
    fetch('/api/version')
        .then(response => response.json())
        .then(data => {
            $('#versionText').text(data.version);
            $('#currentVersion').text(data.version);
            $('#releaseDate').text(data.release_date);
            // 使用service_start_time作为服务启动时间
            $('#serviceStartTime').text(data.service_start_time);
        })
        .catch(error => {
            console.error('获取版本信息失败:', error);
        });
}
```

## 部署注意事项

### 1. 文件结构
确保打包后的目录结构如下：
```
your_app/
├── your_app.exe
├── version.json          # 版本信息文件
└── static/
    └── index.html
```

### 2. version.json格式
```json
{
  "version": "1.0.8",
  "build_time": "2025-07-04 17:36:26",
  "build_count": 19,
  "last_build": "2025-07-04T17:36:26.573313"
}
```

### 3. 服务器部署检查清单
- [ ] 确保`version.json`文件与exe在同一目录
- [ ] 检查文件权限，确保应用可以读取`version.json`
- [ ] 运行exe后检查控制台输出，查看版本信息加载日志
- [ ] 访问网页，检查右上角版本信息是否正确显示

## 验证方法

### 1. 本地验证
1. 打包exe
2. 运行exe
3. 访问网页，点击右上角版本徽章
4. 检查"服务启动"时间是否为exe启动时间（而不是构建时间）

### 2. 服务器验证
1. 将exe和version.json复制到服务器
2. 运行exe
3. 访问网页，检查版本信息：
   - 当前版本：应显示正确的版本号（如v1.0.8）
   - 发布日期：应显示构建时间
   - 服务启动：应显示exe启动时间
   - 不应出现"开发版本"或"vv1.0.0"等错误信息

## 调试信息

修复后的代码包含详细的调试日志，格式为`[版本信息] 消息内容`。如果遇到问题，请查看控制台输出以获取详细信息。

## 修复效果

- ✅ 服务启动时间现在正确显示为exe启动时间
- ✅ 服务器环境下版本信息能够正确显示
- ✅ 消除了版本号重复前缀的问题
- ✅ 增强了错误处理和环境兼容性
- ✅ 添加了详细的调试日志便于问题排查
