#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带版本管理的打包脚本
"""

import os
import sys
import subprocess
import shutil
from version_manager import increment_version, get_version_info

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除: {dir_name}")

def update_version():
    """更新版本号"""
    print("🔢 更新版本号...")
    try:
        new_version, build_time, build_count = increment_version()
        return new_version, build_time, build_count
    except Exception as e:
        print(f"   ❌ 版本更新失败: {e}")
        return None, None, None

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")
    
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--clean',
        '--noconfirm',
        '--add-data', 'static;static',
        '--add-data', 'version.json;.',
        '--name', 'TestDataAnalysisSystem',
        'app.py'
    ]
    
    print(f"   命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("   ✅ 构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ 构建失败: {e}")
        if e.stderr:
            print(f"   错误输出: {e.stderr}")
        return False

def copy_files_to_dist():
    """复制文件到dist目录"""
    print("📁 复制文件到dist目录...")
    
    files_to_copy = [
        'version.json',
        'access_log.txt'
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            try:
                dest_path = os.path.join('dist', file_name)
                shutil.copy2(file_name, dest_path)
                print(f"   复制: {file_name} -> {dest_path}")
            except Exception as e:
                print(f"   复制失败 {file_name}: {e}")
        else:
            print(f"   跳过不存在的文件: {file_name}")

def check_output():
    """检查输出文件"""
    print("📄 检查输出文件...")
    
    exe_path = os.path.join('dist', 'TestDataAnalysisSystem.exe')
    if os.path.exists(exe_path):
        size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"   ✅ exe文件: {exe_path} ({size:.1f} MB)")
    else:
        print("   ❌ 未找到exe文件")
        return False
    
    version_path = os.path.join('dist', 'version.json')
    if os.path.exists(version_path):
        print(f"   ✅ 版本文件: {version_path}")
    else:
        print("   ❌ 未找到版本文件")
    
    return True

def show_version_info():
    """显示版本信息"""
    print("📋 版本信息:")
    try:
        version_info = get_version_info()
        print(f"   版本: {version_info['version']}")
        print(f"   构建时间: {version_info['build_time']}")
        print(f"   构建次数: {version_info['build_count']}")
    except Exception as e:
        print(f"   获取版本信息失败: {e}")

def main():
    """主函数"""
    print("🚀 开始带版本管理的打包流程...")
    print(f"🐍 Python版本: {sys.version}")
    
    # 1. 显示当前版本信息
    print("\n📋 当前版本信息:")
    show_version_info()
    
    # 2. 清理构建目录
    print("\n" + "="*50)
    clean_build()
    
    # 3. 更新版本号
    print("\n" + "="*50)
    new_version, build_time, build_count = update_version()
    if not new_version:
        print("❌ 版本更新失败，退出")
        return False
    
    # 4. 构建exe
    print("\n" + "="*50)
    if not build_exe():
        print("❌ 构建失败，退出")
        return False
    
    # 5. 复制文件到dist目录
    print("\n" + "="*50)
    copy_files_to_dist()
    
    # 6. 检查输出文件
    print("\n" + "="*50)
    if not check_output():
        print("❌ 输出文件检查失败")
        return False
    
    # 7. 显示最终版本信息
    print("\n" + "="*50)
    print("✅ 打包完成!")
    show_version_info()
    print(f"📂 输出目录: {os.path.abspath('dist')}")
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n❌ 打包过程中出现错误")
        input("按回车键退出...")
    else:
        print("\n🎉 打包成功完成!")
        input("按回车键退出...")
