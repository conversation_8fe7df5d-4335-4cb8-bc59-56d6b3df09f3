#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析服务 - 简化版
"""

import json
import requests
from datetime import datetime
from typing import Dict, Any, List

class AIAnalyzer:
    """AI分析器 - 简化版"""
    
    def __init__(self, get_db_connection):
        self.get_db_connection = get_db_connection
        self.api_key = 'sk-b28e0b5d4412410db203c87809ccb9ad'
        self.api_url = 'https://api.deepseek.com/v1/chat/completions'
    
    def analyze(self, time_range: str, station: str = '', slave: str = '', limit: int = 10) -> Dict[str, Any]:
        """执行AI分析"""
        try:
            print(f"🤖 开始AI分析，时间范围: {time_range}, 站位: {station}")
            
            # 1. 收集数据
            analysis_data = self._collect_analysis_data(time_range, station, slave, limit)
            
            # 2. 构建AI提示
            prompt = self._build_analysis_prompt(analysis_data)
            
            # 3. 调用AI服务
            ai_response = self._call_ai_service(prompt)
            
            return {
                'success': True,
                'analysis': ai_response,
                'data_summary': {
                    'total_tests': analysis_data.get('total_tests', 0),
                    'failed_tests': analysis_data.get('failed_tests', 0),
                    'fail_rate': analysis_data.get('fail_rate', 0),
                    'top_issues_count': len(analysis_data.get('top_issues', []))
                },
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            print(f"❌ AI分析失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def _collect_analysis_data(self, time_range: str, station: str, slave: str, limit: int) -> Dict[str, Any]:
        """收集分析数据 - 增强版，包含更详细的失败信息"""
        print(f"📊 收集分析数据...")

        # 构建时间条件
        time_condition, params = self._build_time_condition(time_range)

        # 构建站位条件
        station_condition = self._build_station_condition(station, params)

        conn = self.get_db_connection()
        cursor = conn.cursor()

        try:
            # 获取总体统计
            stats_query = f"""
            SELECT
                COUNT(*) as total_tests,
                SUM(CASE WHEN TestResult = 'Passed' THEN 1 ELSE 0 END) as passed_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) as failed_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as fail_rate
            FROM lp8155_eol_log
            WHERE 1=1 {time_condition} {station_condition}
            """

            cursor.execute(stats_query, params)
            stats = cursor.fetchone()

            # 获取Top Issues
            top_issues_query = f"""
            SELECT
                SUBSTRING_INDEX(Failure_item, ',', 1) as failure_item,
                COUNT(*) as count
            FROM lp8155_eol_log
            WHERE TestResult = 'Failed'
            AND Failure_item IS NOT NULL
            AND Failure_item != ''
            AND Failure_item != '-'
            {time_condition}
            {station_condition}
            GROUP BY SUBSTRING_INDEX(Failure_item, ',', 1)
            ORDER BY count DESC
            LIMIT %s
            """

            cursor.execute(top_issues_query, params + [limit])
            top_issues = cursor.fetchall()

            # 获取站位分布统计（用于设备集中性分析）
            station_stats_query = f"""
            SELECT
                Station,
                COUNT(*) as total_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) as failed_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as fail_rate
            FROM lp8155_eol_log
            WHERE 1=1 {time_condition} {station_condition}
            GROUP BY Station
            ORDER BY fail_rate DESC
            """

            cursor.execute(station_stats_query, params)
            station_stats = cursor.fetchall()

            # 获取时间趋势数据（按天统计）
            time_trend_query = f"""
            SELECT
                DATE(TestTime) as test_date,
                COUNT(*) as total_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) as failed_tests,
                SUM(CASE WHEN TestResult = 'Failed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as fail_rate
            FROM lp8155_eol_log
            WHERE 1=1 {time_condition} {station_condition}
            GROUP BY DATE(TestTime)
            ORDER BY test_date DESC
            LIMIT 30
            """

            cursor.execute(time_trend_query, params)
            time_trends = cursor.fetchall()

            # 获取详细的失败项信息，包含测试值、限值等详细数据
            detailed_failures = []
            for issue in top_issues[:5]:  # 只分析前5个主要失败项
                failure_item = issue[0]

                # 获取该失败项的详细信息 - 获取更多数据用于详细分析
                detail_query = f"""
                SELECT
                    TestTime,
                    Serial_Number,
                    Station,
                    Failure_item
                FROM lp8155_eol_log
                WHERE TestResult = 'Failed'
                AND SUBSTRING_INDEX(Failure_item, ',', 1) = %s
                {time_condition}
                {station_condition}
                ORDER BY TestTime DESC
                LIMIT 100
                """

                detail_params = [failure_item] + params
                cursor.execute(detail_query, detail_params)
                details = cursor.fetchall()

                print(f"🔍 失败项 '{failure_item}' 获取到 {len(details)} 条详细记录")

                # 解析详细的测试数据
                parsed_test_data = []
                station_distribution = {}
                test_value_stats = []
                sub_test_items = {}  # 存储测试子项统计

                print(f"📋 开始解析 {len(details)} 条失败记录...")

                for detail in details:
                    station = detail[2]
                    station_distribution[station] = station_distribution.get(station, 0) + 1

                    # 解析Failure_item字段
                    failure_data = self._parse_failure_item(detail[3])
                    if failure_data:
                        failure_data.update({
                            'time': detail[0].strftime('%Y-%m-%d %H:%M:%S') if detail[0] else '',
                            'serial': detail[1],
                            'station': detail[2],
                            'full_failure_item': detail[3]  # 保存完整的失败项信息
                        })
                        parsed_test_data.append(failure_data)

                        # 收集测试值统计
                        if failure_data.get('measured_value') is not None:
                            test_value_stats.append(failure_data['measured_value'])

                        # 统计测试子项（step_name）
                        step_name = failure_data.get('step_name', '')
                        if step_name:
                            sub_test_items[step_name] = sub_test_items.get(step_name, 0) + 1

                print(f"✅ 解析完成，获得 {len(parsed_test_data)} 个有效测试数据")
                print(f"📊 测试子项统计: {dict(list(sub_test_items.items())[:10])}")
                if test_value_stats:
                    print(f"📈 测试值范围: {min(test_value_stats):.3f} ~ {max(test_value_stats):.3f}")
                    print(f"📈 平均测试值: {sum(test_value_stats)/len(test_value_stats):.3f}")

                # 计算测试值统计信息
                value_analysis = {}
                if test_value_stats:
                    value_analysis = {
                        'count': len(test_value_stats),
                        'min_value': min(test_value_stats),
                        'max_value': max(test_value_stats),
                        'avg_value': sum(test_value_stats) / len(test_value_stats),
                        'value_range': max(test_value_stats) - min(test_value_stats)
                    }

                # 分析测试步骤分布
                step_distribution = {}
                limit_analysis = {}

                for data in parsed_test_data:
                    step_name = data.get('step_name', '')
                    if step_name:
                        step_distribution[step_name] = step_distribution.get(step_name, 0) + 1

                    # 分析限值信息
                    if data.get('low_limit') is not None and data.get('high_limit') is not None:
                        limit_key = f"{data.get('low_limit')}~{data.get('high_limit')}"
                        limit_analysis[limit_key] = limit_analysis.get(limit_key, 0) + 1

                detailed_failures.append({
                    'failure_item': failure_item,
                    'total_count': issue[1],
                    'recent_occurrences': len(details),
                    'station_distribution': dict(sorted(station_distribution.items(), key=lambda x: x[1], reverse=True)),
                    'step_distribution': dict(sorted(step_distribution.items(), key=lambda x: x[1], reverse=True)[:10]),
                    'limit_analysis': dict(sorted(limit_analysis.items(), key=lambda x: x[1], reverse=True)[:5]),
                    'value_analysis': value_analysis,
                    'sample_test_data': parsed_test_data[:10],  # 前10个详细测试数据样本
                    'sample_details': [
                        {
                            'time': data['time'],
                            'serial': data['serial'],
                            'station': data['station'],
                            'step_name': data.get('step_name', ''),
                            'standard_value': data.get('standard_value'),
                            'measured_value': data.get('measured_value'),
                            'low_limit': data.get('low_limit'),
                            'high_limit': data.get('high_limit'),
                            'result': data.get('result', ''),
                            'test_time': data.get('test_time')
                        } for data in parsed_test_data[:5]  # 只取前5个样本
                    ]
                })

            return {
                'total_tests': stats[0] if stats else 0,
                'passed_tests': stats[1] if stats else 0,
                'failed_tests': stats[2] if stats else 0,
                'fail_rate': round(stats[3], 2) if stats and stats[3] else 0,
                'top_issues': [{'failure_item': row[0], 'count': row[1]} for row in top_issues],
                'detailed_failures': detailed_failures,
                'station_stats': [
                    {
                        'station': row[0],
                        'total_tests': row[1],
                        'failed_tests': row[2],
                        'fail_rate': round(row[3], 2) if row[3] else 0
                    } for row in station_stats
                ],
                'time_trends': [
                    {
                        'date': row[0].strftime('%Y-%m-%d') if row[0] else '',
                        'total_tests': row[1],
                        'failed_tests': row[2],
                        'fail_rate': round(row[3], 2) if row[3] else 0
                    } for row in time_trends
                ],
                'time_range': time_range,
                'station': station,
                'analysis_scope': {
                    'time_range_desc': self._get_time_range_description(time_range),
                    'station_filter': station if station else '全部站位',
                    'data_quality': 'enhanced' if detailed_failures else 'basic'
                }
            }

        finally:
            cursor.close()
            conn.close()

    def _get_time_range_description(self, time_range: str) -> str:
        """获取时间范围描述"""
        descriptions = {
            '0': '今天',
            '1': '昨天',
            '7': '最近7天',
            '30': '最近30天',
            '90': '最近90天'
        }
        return descriptions.get(time_range, f'最近{time_range}天')

    def _build_time_condition(self, time_range: str):
        """构建时间条件"""
        if time_range == '0':  # 今天
            return " AND DATE(TestTime) = CURDATE()", []
        elif time_range == '1':  # 昨天
            return " AND DATE(TestTime) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)", []
        elif time_range == '7':  # 最近7天
            return " AND TestTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)", []
        elif time_range == '30':  # 最近30天
            return " AND TestTime >= DATE_SUB(NOW(), INTERVAL 30 DAY)", []
        else:
            return "", []
    
    def _build_station_condition(self, station: str, params: list):
        """构建站位条件"""
        if station == 'SOC':
            # SOC测试：EOL1, EOL2, EOL3, EOL5 (支持带#号和不带#号)
            return " AND Station IN ('EOL1', 'EOL2', 'EOL3', 'EOL5', 'EOL#1', 'EOL#2', 'EOL#3', 'EOL#5')"
        elif station == 'VIU':
            # VIU测试：EOL4 (支持带#号和不带#号)
            return " AND Station IN ('EOL4', 'EOL#4')"
        elif station:
            # 其他特定站位
            params.append(station)
            return " AND Station = %s"
        return ""

    def _parse_failure_item(self, failure_item: str) -> Dict[str, Any]:
        """解析Failure_item字段，提取详细测试信息

        格式: Test Seq,step_name,StandardValue,MeasuredValue,LowLimit,HighLimit,Result,Time
        例如: section_4.8.2 bluetooth,BTMusicPlay_AMP,2.500,1.96731687534531,2,3.0,Fail,0.003686
        """
        if not failure_item or failure_item.strip() == '' or failure_item.strip() == '-':
            return None

        try:
            parts = failure_item.split(',')
            if len(parts) < 8:
                return None

            # 安全地转换数值，处理可能的转换错误
            def safe_float(value, default=None):
                try:
                    return float(value.strip()) if value.strip() else default
                except (ValueError, AttributeError):
                    return default

            return {
                'test_seq': parts[0].strip(),
                'step_name': parts[1].strip(),
                'standard_value': safe_float(parts[2]),
                'measured_value': safe_float(parts[3]),
                'low_limit': safe_float(parts[4]),
                'high_limit': safe_float(parts[5]),
                'result': parts[6].strip(),
                'test_time': safe_float(parts[7])
            }
        except Exception as e:
            print(f"解析Failure_item失败: {failure_item}, 错误: {e}")
            return None

    def _build_analysis_prompt(self, data: Dict[str, Any]) -> str:
        """构建AI分析提示 - 增强版，包含详细失败信息"""

        # 🔍 打印传给AI的完整数据结构
        print("\n" + "="*80)
        print("🤖 AI分析数据详细内容")
        print("="*80)

        print(f"📊 基础统计数据:")
        print(f"  - 测试总数: {data.get('total_tests', 0)}")
        print(f"  - 通过测试: {data.get('passed_tests', 0)}")
        print(f"  - 失败测试: {data.get('failed_tests', 0)}")
        print(f"  - 失败率: {data.get('fail_rate', 0)}%")

        print(f"\n📈 Top Issues数据:")
        top_issues = data.get('top_issues', [])
        for i, issue in enumerate(top_issues[:5], 1):
            print(f"  {i}. {issue.get('failure_item', 'N/A')}: {issue.get('count', 0)}次")

        print(f"\n🏭 站位分布数据:")
        station_stats = data.get('station_stats', [])
        for station in station_stats[:5]:
            print(f"  - {station.get('station', 'N/A')}: 总测试{station.get('total_tests', 0)}次, 失败{station.get('failed_tests', 0)}次, 失败率{station.get('fail_rate', 0):.1f}%")

        print(f"\n📅 时间趋势数据:")
        time_trends = data.get('time_trends', [])
        for trend in time_trends[:5]:
            print(f"  - {trend.get('date', 'N/A')}: 总测试{trend.get('total_tests', 0)}次, 失败{trend.get('failed_tests', 0)}次, 失败率{trend.get('fail_rate', 0):.1f}%")

        print(f"\n🔬 详细失败分析数据:")
        detailed_failures = data.get('detailed_failures', [])
        for i, failure in enumerate(detailed_failures[:3], 1):
            print(f"\n  === 失败项 {i}: {failure.get('failure_item', 'N/A')} ===")
            print(f"    总计次数: {failure.get('total_count', 0)}")
            print(f"    最近发生: {failure.get('recent_occurrences', 0)}次")

            # 站位分布
            station_dist = failure.get('station_distribution', {})
            if station_dist:
                print(f"    站位分布: {dict(list(station_dist.items())[:3])}")

            # 测试步骤分布
            step_dist = failure.get('step_distribution', {})
            if step_dist:
                print(f"    测试步骤分布 (共{len(step_dist)}个子项): {dict(list(step_dist.items())[:5])}")

            # 限值分析
            limit_analysis = failure.get('limit_analysis', {})
            if limit_analysis:
                print(f"    限值分布: {dict(list(limit_analysis.items())[:3])}")

            # 测试值统计
            value_analysis = failure.get('value_analysis', {})
            if value_analysis and value_analysis.get('count', 0) > 0:
                print(f"    测试值统计:")
                print(f"      样本数量: {value_analysis.get('count', 0)}")
                print(f"      测试值范围: {value_analysis.get('min_value', 0):.3f} ~ {value_analysis.get('max_value', 0):.3f}")
                print(f"      平均值: {value_analysis.get('avg_value', 0):.3f}")
                print(f"      分散度: {value_analysis.get('value_range', 0):.3f}")

            # 详细测试样本
            samples = failure.get('sample_details', [])
            if samples:
                print(f"    详细测试样本 (前5个):")
                for j, sample in enumerate(samples[:5], 1):
                    print(f"      样本{j}: {sample.get('time', 'N/A')} | {sample.get('serial', 'N/A')} | {sample.get('station', 'N/A')}")
                    if sample.get('step_name'):
                        print(f"        测试子项: {sample.get('step_name', 'N/A')}")
                        print(f"        标准值: {sample.get('standard_value', 'N/A')} | 实测值: {sample.get('measured_value', 'N/A')}")
                        print(f"        下限: {sample.get('low_limit', 'N/A')} | 上限: {sample.get('high_limit', 'N/A')} | 结果: {sample.get('result', 'N/A')}")
                        print(f"        完整失败项: {sample.get('full_failure_item', 'N/A')[:100]}...")
                    else:
                        print(f"        原始数据: {sample.get('full_failure_item', 'N/A')[:100]}...")

        print("="*80)
        print("🤖 以上是传给AI的完整数据内容")
        print("="*80 + "\n")

        scope = data.get('analysis_scope', {})

        prompt = f"""
请分析以下零跑汽车EOL测试数据并提供专业的质量分析报告：

## 数据概览
- 测试总数: {data['total_tests']}
- 通过测试: {data['passed_tests']}
- 失败测试: {data['failed_tests']}
- 失败率: {data['fail_rate']}%
- 分析范围: {scope.get('time_range_desc', data['time_range'])}
- 测试站位: {scope.get('station_filter', data['station'] or '全部')}
- 数据质量: {scope.get('data_quality', 'basic')}

## 主要失败项统计
"""

        for i, issue in enumerate(data['top_issues'][:5], 1):
            prompt += f"{i}. {issue['failure_item']}: {issue['count']}次\n"

        # 添加站位分布统计
        station_stats = data.get('station_stats', [])
        if station_stats:
            prompt += "\n## 测试站位分布统计\n"
            for station in station_stats[:8]:  # 显示前8个站位
                prompt += f"- {station['station']}: 总测试{station['total_tests']}次, 失败{station['failed_tests']}次, 失败率{station['fail_rate']}%\n"

        # 添加时间趋势数据
        time_trends = data.get('time_trends', [])
        if time_trends:
            prompt += "\n## 时间趋势数据（最近几天）\n"
            for trend in time_trends[:10]:  # 显示最近10天
                prompt += f"- {trend['date']}: 总测试{trend['total_tests']}次, 失败{trend['failed_tests']}次, 失败率{trend['fail_rate']}%\n"

        # 添加详细失败信息（如果有的话）
        detailed_failures = data.get('detailed_failures', [])
        if detailed_failures:
            prompt += "\n## 详细失败分析\n"

            for i, failure in enumerate(detailed_failures[:3], 1):  # 只分析前3个
                prompt += f"\n### {i}. {failure['failure_item']} (总计{failure['total_count']}次)\n"
                prompt += f"- 最近发生次数: {failure['recent_occurrences']}次\n"

                # 添加站位分布
                station_dist = failure.get('station_distribution', {})
                if station_dist:
                    prompt += "- 站位分布:\n"
                    for station, count in list(station_dist.items())[:5]:
                        prompt += f"  * {station}: {count}次\n"

                # 添加测试步骤分布（详细的测试子项）
                step_dist = failure.get('step_distribution', {})
                if step_dist:
                    prompt += f"- 测试子项详细分布 (共{len(step_dist)}个子项):\n"
                    for step, count in list(step_dist.items())[:10]:  # 显示更多子项
                        prompt += f"  * {step}: {count}次失败\n"

                # 添加限值分析
                limit_analysis = failure.get('limit_analysis', {})
                if limit_analysis:
                    prompt += "- 测试限值分布:\n"
                    for limit_range, count in list(limit_analysis.items())[:3]:
                        prompt += f"  * 限值范围 {limit_range}: {count}次\n"

                # 添加测试值统计分析
                value_analysis = failure.get('value_analysis', {})
                if value_analysis and value_analysis.get('count', 0) > 0:
                    prompt += "- 测试值统计分析:\n"
                    prompt += f"  * 样本数量: {value_analysis['count']}个\n"
                    prompt += f"  * 测试值范围: {value_analysis['min_value']:.3f} ~ {value_analysis['max_value']:.3f}\n"
                    prompt += f"  * 平均测试值: {value_analysis['avg_value']:.3f}\n"
                    prompt += f"  * 数值分散度: {value_analysis['value_range']:.3f}\n"

                # 添加详细测试数据样本
                samples = failure.get('sample_details', [])
                if samples:
                    prompt += "- 详细测试数据样本 (包含测试子项、测试值、限值等完整信息):\n"
                    for i, sample in enumerate(samples[:5], 1):  # 显示更多样本
                        prompt += f"  样本{i}: {sample['time']} | 序列号:{sample['serial']} | 设备:{sample['station']}\n"
                        if sample.get('step_name') and sample.get('measured_value') is not None:
                            prompt += f"    ├─ 测试子项: {sample['step_name']}\n"
                            prompt += f"    ├─ 标准值: {sample.get('standard_value', 'N/A')}\n"
                            prompt += f"    ├─ 实测值: {sample['measured_value']:.6f}\n"
                            prompt += f"    ├─ 下限: {sample.get('low_limit', 'N/A')} | 上限: {sample.get('high_limit', 'N/A')}\n"
                            prompt += f"    ├─ 测试结果: {sample.get('result', 'N/A')}\n"
                            prompt += f"    └─ 测试时间: {sample.get('test_time', 'N/A')}秒\n"
                        else:
                            # 如果解析失败，显示原始数据
                            full_item = sample.get('full_failure_item', '')
                            if full_item:
                                prompt += f"    └─ 原始数据: {full_item}\n"
                        prompt += "\n"

        prompt += """

## 分析要求
请基于以上数据提供专业的质量分析报告，重点关注以下维度：

1. **时间趋势分析**
   - 分析失败率在时间维度上的变化趋势
   - 识别是否存在周期性或突发性问题
   - 判断问题是持续恶化、改善还是稳定状态
   - 基于时间分布判断是否为批次性问题

2. **设备集中性分析**
   - 分析失败是否集中在特定测试站位/机台
   - 计算各站位的失败率分布差异
   - 识别设备相关问题的特征模式
   - 判断是否需要设备校准或维护

3. **测试值与限值分析**
   - 分析测试值分布特征和统计规律
   - 评估测试值与标准值、限值的偏差程度
   - 识别测试值异常模式（如系统性偏移、随机波动等）
   - 分析限值设置的合理性和测试裕量
   - 判断失败是由于测试值超限还是其他原因

4. **失败模式分类与根因判断**
   - **产品真实不良**:
     * 测试值系统性偏离标准值或超出限值
     * 失败项在多个站位均有发生且测试值一致性差
     * 失败模式具有产品设计或制造相关特征
     * 测试值分布呈现明显的质量问题特征
   - **测试设备问题**:
     * 测试值在特定机台呈现异常分布
     * 失败集中在特定机台或站位但测试值分散
     * 测试值与标准值偏差呈现设备特征性模式
     * 失败项具有测试相关特征（如通信、校准、接触等）
   - **测试流程问题**:
     * 测试值异常与测试条件、环境、操作相关
     * 测试时间异常或测试序列问题

5. **数据质量与可信度评估**
   - 评估测试数据的完整性和一致性
   - 识别异常测试值和可能的数据质量问题
   - 判断样本量是否足够支撑分析结论
   - 评估测试值的可信度和重现性

6. **关联性分析**
   - 分析不同失败项之间的关联性和测试值相关性
   - 识别可能的共同根因和系统性问题
   - 分析失败项与测试序列、测试步骤的关系
   - 评估测试项之间的依赖关系和影响

7. **风险评估与优先级**
   - 基于失败频次、测试值偏差程度评估风险等级
   - 确定需要优先解决的问题（考虑测试值分布特征）
   - 评估问题的紧急程度和潜在影响
   - 分析测试裕量和安全边际

8. **具体改进建议**
   - 针对设备问题：设备维护、校准、更换建议（基于测试值分析）
   - 针对产品问题：设计改进、工艺优化、供应商管理建议
   - 针对流程问题：测试流程优化、标准化建议
   - 针对限值问题：限值调整、测试条件优化建议
   - 预防措施和监控建议（包括测试值监控策略）

## 输出格式要求
- 使用中文回答，格式清晰，逻辑严谨
- 每个分析结论都要有数据支撑
- 提供具体可执行的改进建议
- 突出重点问题和紧急事项
- 避免模糊表述，给出明确判断
"""

        return prompt
    
    def _call_ai_service(self, prompt: str) -> str:
        """调用AI服务"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.api_key}'
            }
            
            data = {
                'model': 'deepseek-chat',
                'messages': [
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': 4000,
                'temperature': 0.3
            }
            
            response = requests.post(
                self.api_url,
                headers=headers,
                json=data,
                timeout=60,
                verify=False
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                return f"AI服务调用失败: HTTP {response.status_code}"
                
        except Exception as e:
            return f"AI服务异常: {str(e)}"

def get_analysis_prompt(data: Dict[str, Any]) -> str:
    """获取分析提示词"""
    analyzer = AIAnalyzer(None)
    return analyzer._build_analysis_prompt(data)
