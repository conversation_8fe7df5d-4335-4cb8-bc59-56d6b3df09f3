:root {
    --bg-color:rgb(71,72,75);
    --bg-light-color: #fff;
    --bg-dark-color: rgb(71,72,75);
    --light-link-color: #fff;
    --dark-link-color: #333;
    --light-font-color: #fff;
    --dark-font-color:#333;
    --color-link: #1352d1;
    --color-link-hover: #111;
}

body, html { font-size: 14px; padding: 0; margin: 0;}
body.light {
    background-color: var(--bg-light-color);
    color: var(--dark-font-color);
}
body.light a{
    color: var(--dark-link-color);
}
body.dark{
    background-color: var(--bg-dark-color);
    color:var(--light-font-color)
}
body.dark a{
    color:var(--light-link-color)
}

/* Reset */
*,
*:after,
*:before {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

ul, li {
    list-style: none;
}



.header {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.related {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 50px;
}

.related .links {
    display: flex;
}
.related .links a {
    display: block;
    max-width: 320px;
    padding: 15px;
    text-decoration: none;
}
.related .links a img {
    width: 300px;
}
.related .links a h3 {
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    
}

.link__content {
    display: flex;
}
.content__item {
    width: 100%;
    height: 100%;
    margin-left: 20px;
    padding: 0;
    counter-increment: itemcounter;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}
.link {
    cursor: pointer;
    font-size: 18px;
    position: relative;
    white-space: nowrap;
    color: var(--color-link);
    text-decoration: none;
}

.link::before,
.link::after {
    position: absolute;
    width: 100%;
    height: 1px;
    background: currentColor;
    top: 100%;
    left: 0;
    pointer-events: none;
}
.link::before {
    content: '';
}

.link--iocaste {
    font-family: lust-fine, sans-serif;
    overflow: hidden;
    padding: 7px 0;
}
.link__graphic {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    fill: none;
    stroke: #fff;
    stroke-width: 1px;
}

body.light .link__graphic {
    stroke: var(--color-link-hover);
}

.link__graphic--slide {
    top: -3px;
    stroke-width: 2px;
    transition: transform 0.7s;
    transition-timing-function: cubic-bezier(0, 0.25, 0.5, 1);
}

.link:hover .link__graphic--slide {
    transform: translate3d(-66.6%, 0, 0);
}

/* AI分析按钮样式 */
.ai-analysis-btn {
    position: relative;
    font-weight: bold;
    font-size: 16px;
    padding: 12px 24px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.ai-analysis-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    background: linear-gradient(45deg, #20c997, #28a745);
}

.ai-analysis-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
}

/* 震动动画 */
@keyframes vibrate {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.ai-analysis-btn.vibrate {
    animation: vibrate 0.5s ease-in-out;
}

/* 脉冲动画 */
@keyframes pulse {
    0% { box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3); }
    50% { box-shadow: 0 4px 25px rgba(40, 167, 69, 0.6); }
    100% { box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3); }
}

.ai-analysis-btn.pulse {
    animation: pulse 2s infinite;
}

/* AI分析结果动画 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.ai-analysis-card {
    animation: slideInUp 0.6s ease-out;
}

.ai-analysis-content {
    animation: fadeInScale 0.8s ease-out;
}

/* 分析完成提示动画 */
@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.analysis-complete-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    animation: bounceIn 0.6s ease-out;
}

.analysis-start-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    background: linear-gradient(45deg, #007bff, #6610f2);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    animation: bounceIn 0.6s ease-out;
    font-size: 16px;
    font-weight: bold;
}

/* AI分析报告标题样式强化 */
#aiAnalysisCard .card-header span {
    color: white !important;
    font-weight: bold !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

#aiAnalysisCard .card-header {
    background: linear-gradient(45deg, #28a745, #20c997) !important;
}