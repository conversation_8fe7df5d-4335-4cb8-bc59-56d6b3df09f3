#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化打包脚本 - 解决依赖冲突问题
"""

import os
import sys
import subprocess
import shutil

def main():
    """主函数"""
    print("🚀 开始最小化打包流程...")
    
    # 清理
    print("🧹 清理构建目录...")
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
    
    # 最基本的打包命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--clean',
        '--noconfirm',
        '--add-data', 'static;static',
        '--name', 'TestDataAnalysisSystem',
        'app.py'
    ]
    
    print("🔨 执行打包命令...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, text=True, capture_output=True)
        print("✅ 打包成功!")
        
        # 复制额外文件
        if os.path.exists('version.json'):
            shutil.copy2('version.json', 'dist/')
            print("📁 复制 version.json")
            
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败!")
        print(f"错误代码: {e.returncode}")
        print(f"标准输出: {e.stdout}")
        print(f"错误输出: {e.stderr}")
        
        # 尝试分析错误
        if "ModuleNotFoundError" in e.stderr:
            print("\n💡 建议:")
            print("1. 检查是否安装了所有依赖: pip install -r requirements.txt")
            print("2. 尝试升级PyInstaller: pip install --upgrade pyinstaller")
        
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        input("按回车键退出...")
