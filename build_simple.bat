@echo off
echo 🚀 开始简化打包流程...

echo 🧹 清理构建目录...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__

echo 📦 安装依赖...
pip install --upgrade pip
pip install --upgrade setuptools
pip install --upgrade pyinstaller
pip install -r requirements.txt

echo 🔨 开始打包...
pyinstaller --clean --onefile ^
    --add-data "static;static" ^
    --add-data "version.json;." ^
    --hidden-import pymysql ^
    --hidden-import pymysql.cursors ^
    --hidden-import pandas ^
    --hidden-import numpy ^
    --hidden-import flask ^
    --hidden-import requests ^
    --hidden-import ai_analysis ^
    --exclude-module tkinter ^
    --exclude-module matplotlib ^
    --exclude-module scipy ^
    --exclude-module IPython ^
    --exclude-module jupyter ^
    --name TestDataAnalysisSystem ^
    app.py

if %errorlevel% equ 0 (
    echo ✅ 打包成功!
    echo 📂 输出目录: dist\
    if exist version.json copy version.json dist\
    if exist access_log.txt copy access_log.txt dist\
) else (
    echo ❌ 打包失败!
)

pause
